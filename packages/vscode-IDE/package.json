{"name": "joycoder-editor", "displayName": "JoyCode", "description": "专注于AI和IDE的研发工具集，为您提供高效、智能、专业的编码辅助服务", "publisher": "JoyCode", "version": "3.0.0", "private": true, "engines": {"vscode": "^1.74.0", "node": ">=18.15.0", "pnpm": ">=8.6.7"}, "categories": ["Other"], "keywords": ["chatgpt", "openai", "copilot", "<PERSON><PERSON>", "jd", "JoyCode", "HiBox", "ai", "autocomplete", "inline completion", "言犀"], "icon": "assets/logo.png", "activationEvents": ["*"], "main": "./dist/extension.js", "extensionKind": ["ui", "workspace"], "sideEffects": false, "capabilities": {"untrustedWorkspaces": {"supported": true}}, "scripts": {"watch:web": "pnpm -C ../../ run watch:web", "watch:extension": "webpack --watch --mode development", "watch": "rimraf ./dist && npm-run-all -p watch:web watch:extension watch:agent-driven watch:agent-setting", "build:web": "pnpm -C ../../ run build:web", "build": "webpack --mode production", "watch:agent-driven": "cross-env PLUGIN_VER=ide pnpm -C ../../ run watch:agent-driven", "build:agent-driven": "cross-env PLUGIN_VER=ide pnpm -C ../../ run build:agent-driven", "watch:agent-setting": "cross-env PLUGIN_VER=ide pnpm -C ../../ run watch:agent-setting", "build:agent-setting": "cross-env PLUGIN_VER=ide pnpm -C ../../ run build:agent-setting", "package": "pnpm build && pnpm build:web && pnpm build:agent-driven && pnpm build:agent-setting && vsce package --allow-star-activation --no-dependencies --allow-missing-repository", "publish": "vsce publish --allow-star-activation --no-dependencies --allow-missing-repository"}, "contributes": {"icons": {"completion-icon": {"description": "completion icon", "default": {"fontPath": "assets/iconfont/iconfont.woff", "fontCharacter": "\\e800"}}, "completion-disabled-icon": {"description": "completion disabled icon", "default": {"fontPath": "assets/iconfont/disabled-iconfont.woff", "fontCharacter": "\\e800"}}}, "inlineCompletions": [{"language": "*", "disallowedPrecedingCharacters": [".", ":", ";"], "provider": {"resolveProvider": true}}], "viewsContainers": {"activitybar": [{"id": "JoyCode", "title": "chat", "icon": "$(chat-editor-label-icon)"}], "panel": []}, "views": {"JoyCode": [{"type": "webview", "id": "JoyCode-left-view", "name": "", "when": "!config.JoyCode.switch.AgentView", "contextualTitle": "JoyCode 聊天"}, {"type": "webview", "id": "joycoder.joycoder.SidebarProvider", "name": "", "when": "config.JoyCode.switch.AgentView", "contextualTitle": "JoyCode"}], "secIssues": [{"id": "secIssues", "name": "File Tree"}]}, "viewsWelcome": [{"view": "JoyCode-left-view", "contents": ""}], "commands": [{"command": "joycode.autoCode.mcp.msg", "title": "mcp相关操作消息", "category": "自动化编程"}, {"command": "joycoder.autoCode.syncState", "title": "设置页推送", "category": "自动化编程"}, {"command": "joycoder.joycoder.openInNewTab", "title": "新的任务", "icon": "$(add)", "category": "自动化编程"}, {"command": "joycoder.joycoder.openInNewChat", "title": "新的会话", "icon": "$(add)", "category": "自动化编程"}, {"command": "joycoder.joycoder.showAuxiliaryBar", "title": "自动化编程", "category": "打开右侧辅助边栏"}, {"command": "joycoder.joycoder.historyButtonClicked", "title": "历史", "icon": "$(history)"}, {"command": "joycoder.Coder.plusButtonClicked", "title": "新建聊天", "icon": "$(add)"}, {"command": "joycoder.Coder.mcpButtonClicked", "title": "MCP 配置", "icon": {"light": "assets/agent/mcp-hub-light.svg", "dark": "assets/agent/mcp-hub-dark.svg"}}, {"command": "divider", "title": "-", "shortTitle": "-"}, {"command": "JoyCode.view.aichat", "title": "JoyCode: AI助手", "shortTitle": "返回AI助手", "icon": {"light": "assets/light/aichat.svg", "dark": "assets/dark/aichat.svg"}}, {"command": "JoyCode.view.nav", "title": "JoyCode: 工具箱", "shortTitle": "工具箱", "icon": {"light": "assets/light/toolbox.svg", "dark": "assets/dark/toolbox.svg"}}, {"command": "JoyCode.code-completions-setting", "title": "JoyCode: 预测补全设置", "icon": {"light": "assets/light/completion.svg", "dark": "assets/dark/completion.svg"}}, {"command": "JoyCode.config.setting", "title": "JoyCode: 设置", "icon": {"light": "assets/light/setting.svg", "dark": "assets/dark/setting.svg"}}, {"command": "JoyCode.changeWorkSpace", "title": "JoyCode: 切换工作区", "shortTitle": "切换工作区"}, {"command": "JoyCode.settingWorkSpace", "title": "JoyCode: 设置工作区", "shortTitle": "设置工作区"}, {"command": "JoyCode.createProject.start", "title": "JoyCode: 快速创建", "shortTitle": "快速创建"}, {"command": "JoyCode.materialImport.start", "title": "JoyCode: 物料导入", "icon": {"light": "assets/treeView/importIcon.svg", "dark": "assets/treeView/importIcon.svg"}}, {"command": "JoyCode.marketplace.show", "title": "JoyCode: 插件市场", "shortTitle": "插件市场"}, {"command": "JoyCode.browser.open", "title": "JoyCode: 打开内置浏览器", "shortTitle": "内置浏览器"}, {"command": "JoyCode.openConfigPage", "title": "JoyCode: 插件配置", "shortTitle": "插件设置"}, {"command": "JoyCode.openGlobalKeybindings", "title": "JoyCode: 快捷键设置", "shortTitle": "快捷键设置"}, {"command": "JoyCode.LogOut", "title": "JoyCode: 退出登录", "shortTitle": "退出登录"}, {"command": "JoyCode.Coder.Logout", "title": "JoyCode: 退出登录", "shortTitle": "退出登录"}, {"command": "JoyCode.ClearGlobalState", "title": "JoyCode: 清除全局状态", "shortTitle": "清除缓存"}, {"command": "JoyCode.downloadHistory", "title": "JoyCode: 导出历史会话", "shortTitle": "导出历史会话"}, {"command": "JoyCode.checkUpdate", "title": "JoyCode: 检查更新", "shortTitle": "检查更新"}, {"command": "JoyCode.openHomePage", "title": "JoyCode: 官方文档", "shortTitle": "官方文档"}, {"command": "JoyCode.openFAQ", "title": "JoyCode: 常见问题", "shortTitle": "常见问题"}, {"command": "JoyCode.openME", "title": "JoyCode: 咚咚群", "shortTitle": "咚咚群"}, {"command": "JoyCode.bugReport", "title": "JoyCode: 问题反馈", "shortTitle": "问题反馈"}, {"command": "JoyCode.ai.inlineChat.enter", "title": "提交"}, {"command": "JoyCode.ai.inlineChat.delete", "title": "关闭"}, {"command": "JoyCode.ai.inlineChat.abort", "title": "中断问答"}, {"command": "JoyCode.uploadImage", "title": "【上传图片】上传本地图片"}, {"command": "JoyCode.pasteAndUploadImage", "title": "【上传图片】上传剪切板图片"}, {"command": "JoyCode.quickConsole", "title": "JoyCode: 快速console"}, {"command": "JoyCode.ai.inlineChat.create", "title": "JoyCode: 打开InlineChat"}, {"command": "JoyCode.cssModuleTransform", "title": "转为CSS Module"}, {"command": "JoyCode.html2scss", "title": "JoyCode: 将HTML转为CSS/SCSS/LESS"}, {"command": "JoyCode.quickJump.search", "title": "【文档】快速搜索"}, {"command": "JoyCode.ai.chat.ask", "title": "智能问答", "icon": {"light": "assets/joycode-light-talk.svg", "dark": "assets/joycode-dark-talk.svg"}}, {"command": "JoyCode.quickJump.searchH5BadJs", "title": "H5 BadJs"}, {"command": "JoyCode.quickJump.searchXcxBadJs", "title": "小程序BadJs"}, {"command": "JoyCode.quickJump.openGitOrigin", "title": "【Coding】在远程仓库查看该文件"}, {"command": "JoyCode.quickJump.diffGitOrigin", "title": "【Coding】创建MR或在远程仓库Diff该分支"}, {"command": "JoyCode.cli.quick.command", "title": "JoyCode: 快捷命令行"}, {"command": "JoyCode.cli.custom.command", "title": "JoyCode: 自定义命令行"}, {"command": "JoyCode.cli.killall", "title": "关闭所有JoyCode命令行"}, {"command": "JoyCode.duplicateCodeDetection", "title": "代码重复检测"}, {"command": "JoyCode.unusedTsExportDetection", "title": "未引用代码检测(TS)"}, {"command": "JoyCode.unusedFileDetection", "title": "未引用文件检测"}, {"command": "JoyCode.ai.d2c", "title": "JoyCode: D2C"}, {"command": "JoyCode.ai.chat.explain", "title": "解释代码"}, {"command": "JoyCode.ai.chat.broken", "title": "报错分析"}, {"command": "JoyCode.ai.chat.refactor", "title": "优化代码"}, {"command": "JoyCode.ai.chat.test", "title": "生成单测"}, {"command": "JoyCode.ai.chat.doc", "title": "文档生成"}, {"command": "JoyCode.ai.chat.review", "title": "代码评审"}, {"command": "JoyCode.ai.code.review", "title": "JoyCode代码评审", "icon": {"light": "assets/code-review.svg", "dark": "assets/code-review.svg"}}, {"command": "JoyCode.ai.commit.message", "title": "JoyCode生成提交消息", "icon": {"light": "assets/code-review-message.svg", "dark": "assets/code-review-message.svg"}}, {"command": "JoyCode.ai.chat.autoFill", "title": "代码自动填充"}, {"command": "JoyCode.ai.chat.other", "title": "其他"}, {"command": "joycoder.switch.chat", "title": "切回旧版", "icon": {"light": "assets/light/chat-view.svg", "dark": "assets/dark/chat-view.svg"}}, {"command": "joycoder.switch.Coder", "title": "切回Coder", "icon": {"light": "assets/light/agent-chat.svg", "dark": "assets/dark/agent-chat.svg"}}, {"command": "JoyCode.code-completions", "title": "代码预测"}, {"command": "JoyCode.code-completions.row", "title": "代码预测按行补全"}, {"command": "JoyCode.completion.formatAccept", "title": "代码预测采纳执行事件"}, {"command": "JoyCode.completion.get-new-completion", "title": "代码预测自动执行事件"}, {"command": "JoyCode.code-completion.manually", "title": "代码预测主动触发"}, {"command": "JoyCode.browser.open", "title": "JoyCode: 打开内置浏览器"}, {"command": "JoyCode.browser.preview.explorer", "title": "JoyCode: 预览调试"}, {"command": "JoyCode.browser.preview", "title": "【预览调试】预览调试H5页面"}, {"command": "JoyCode.browser.clearData", "title": "JoyCode: 清除内置浏览器缓存数据"}, {"command": "JoyCode.fixbug.clearDiffHighlights", "title": "fixbug: 清除diff高亮"}, {"command": "JoyCode.fixbug.acceptChange", "title": "fixbug: 采纳"}, {"command": "JoyCode.fixbug.rejectChange", "title": "fixbug: 拒绝"}, {"command": "JoyCode.codelens.optimization", "title": "代码优化"}, {"command": "JoyCode.codelens.functionComment", "title": "函数注释"}, {"command": "JoyCode.codelens.comment", "title": "逐行注释"}, {"command": "JoyCode.codelens.reconstruction", "title": "代码重构"}, {"title": "解释代码", "command": "JoyCode.codelens.explain"}, {"title": "单元测试", "command": "JoyCode.codelens.test"}, {"title": "删除当前行间菜单", "command": "JoyCode.codelens.del"}, {"title": "删除loading状态", "command": "JoyCode.codelens.del.loading"}, {"title": "代码评审", "command": "JoyCode.codelens.codeReview"}, {"title": "代码评审-errorLine", "command": "JoyCode.codeReview.codeLensOnClick"}, {"command": "JoyCoderFE.multi.translation.command", "title": "取消转换", "icon": {"light": "assets/translation/cancel.svg", "dark": "assets/translation/cancel.svg"}}, {"command": "JoyCode.ai.chat.transformCodeFlutter3", "title": "转为Flutter3"}, {"command": "JoyCode.ai.chat.transformCodeJava", "title": "转为Java"}, {"command": "JoyCode.ai.chat.transformCodeJS", "title": "转为JavaScript"}, {"command": "JoyCode.ai.chat.transformCodeTS", "title": "转为TypeScript"}, {"command": "JoyCode.ai.chat.optimizedHTML", "title": "HTML语义优化"}, {"command": "JoyCode.ai.transformCodeVue2", "title": "转为Vue2.x"}, {"command": "JoyCode.ai.transformCodeVue3TS", "title": "转为Vue3.x"}, {"command": "JoyCode.ai.transformCodeReactTS", "title": "转为React"}, {"command": "JoyCode.ai.transformCodeTaroReactTS", "title": "转为Taro"}, {"command": "JoyCode.ai.transformCodeTaroVueTS", "title": "转为Taro-Vue"}, {"command": "JoyCode.ai.transformCodeFlutter3", "title": "转为Flutter3"}, {"command": "JoyCode.ai.transformCodeHarmonyArkTS", "title": "转为鸿蒙-ArkTS"}, {"command": "JoyCode.ai.chat.transformCodeVue", "title": "转为Vue2.x"}, {"command": "JoyCode.ai.chat.transformCodeVue2TS", "title": "转为Vue2.x-TS"}, {"command": "JoyCode.ai.chat.transformCodeVue3", "title": "转为Vue3.x"}, {"command": "JoyCode.ai.chat.transformCodeVue3TS", "title": "转为Vue3.x-TS"}, {"command": "JoyCode.ai.chat.transformCodeReact", "title": "转为React"}, {"command": "JoyCode.ai.chat.transformCodeReactTS", "title": "转为React-TS"}, {"command": "JoyCode.ai.chat.transformCodeTaro", "title": "转为Taro"}, {"command": "JoyCode.ai.chat.transformCodeTaroVueTS", "title": "转为Taro-Vue-TS"}, {"command": "JoyCode.ai.chat.transformCodeTaroReactTS", "title": "转为Taro-React-TS"}, {"command": "JoyCode.ai.chat.transformCodeHarmonyArkTS", "title": "转为鸿蒙-ArkTS"}, {"command": "JoyCoderFE.sec.showSecIssues", "title": "神医安全检查问题"}, {"command": "JoyCoderFE.sec.showCommitHistory", "title": "查看神医commit扫描历史", "icon": "assets/sec/history.svg"}, {"command": "JoyCoderFE.translation.cancelAll", "title": "取消所有操作", "icon": {"dark": "assets/translation/cancel-dark.svg", "light": "assets/translation/cancel-light.svg"}}, {"command": "JoyCoderFE.translation.replaceAll", "title": "一键替换", "icon": {"dark": "assets/translation/replace-dark.svg", "light": "assets/translation/replace-light.svg"}}, {"command": "JoyCode.ai.terminal.explain", "title": "JoyCode: 解释选中内容"}, {"command": "JoyCode.ai.log.reportImmediate", "title": "JoyCode采纳率上报命令"}], "menus": {"view/item/context": [{"command": "JoyCoderFE.multi.translation.command"}], "terminal/context": [{"command": "JoyCode.ai.terminal.explain", "group": "navigation@01"}], "comments/commentThread/context": [{"command": "JoyCode.ai.inlineChat.enter", "group": "inline", "when": "commentController == joycoder-inline-chat"}], "comments/commentThread/title": [{"command": "JoyCode.ai.inlineChat.abort", "group": "navigation@1", "when": "commentController == joycoder-inline-chat && when.JoyCode.inlineChat.streaming"}, {"command": "JoyCode.ai.inlineChat.delete", "group": "navigation@2", "when": "commentController == joycoder-inline-chat"}], "view/title": [{"command": "joycoder.Coder.plusButtonClicked", "group": "navigation@1", "when": "view == joycoder.joycoder.SidebarProvider"}, {"command": "joycoder.joycoder.historyButtonClicked", "group": "navigation@2", "when": "view == joycoder.joycoder.SidebarProvider"}, {"command": "JoyCode.view.aichat", "when": "view == JoyCode-left-view && when.JoyCode.view.nav", "group": "navigation@2"}, {"command": "JoyCode.view.nav", "when": "view == JoyCode-left-view && when.JoyCode.view.aichat", "group": "navigation@3"}, {"command": "joycoder.switch.Coder", "group": "navigation@4", "when": "view == JoyCode-left-view"}, {"command": "JoyCode.config.setting", "when": "view == JoyCode-left-view || view == joycoder.joycoder.SidebarProvider", "group": "navigation@6"}, {"when": "view == secIssues", "command": "JoyCoderFE.sec.showCommitHistory", "group": "navigation"}], "editor/title": [], "scm/title": [{"command": "JoyCode.ai.commit.message", "group": "navigation@1"}, {"command": "JoyCode.ai.code.review", "group": "navigation@2"}], "explorer/context": [{"command": "JoyCode.browser.preview.explorer", "group": "AJoyCoder@1", "when": "resourceLangId =~ /html/"}], "editor/context": [{"submenu": "JoyCode.ai.chat.context", "group": "AJoyCoder@1"}], "JoyCode.badJs.context": [{"command": "JoyCode.quickJump.searchH5BadJs", "group": "AJoyCoder@1", "when": "when.JoyCode.quickJump.searchH5BadJs"}, {"command": "JoyCode.quickJump.searchXcxBadJs", "group": "AJoyCoder@1", "when": "when.JoyCode.quickJump.searchXcxBadJs"}], "JoyCode.code.detection": [{"command": "JoyCode.duplicateCodeDetection", "group": "AJoyCoder@1"}, {"command": "JoyCode.unusedTsExportDetection", "group": "AJoyCoder@2"}, {"command": "JoyCode.unusedFileDetection", "group": "AJoyCoder@3"}], "JoyCode.ai.chat.context": [{"command": "JoyCode.ai.chat.explain", "group": "AJoyCoder@01"}, {"command": "JoyCode.ai.chat.broken", "group": "AJoyCoder@02"}, {"command": "JoyCode.ai.chat.refactor", "group": "AJoyCoder@03"}, {"command": "JoyCode.ai.chat.test", "group": "AJoyCoder@04"}, {"command": "JoyCode.ai.chat.doc", "group": "AJoyCoder@05"}, {"command": "JoyCode.ai.chat.review", "group": "AJoyCoder@06"}, {"command": "JoyCode.ai.chat.other", "group": "AJoyCoder@7"}, {"command": "JoyCode.ai.chat.autoFill", "group": "BJoyCoder@1", "when": "editorHasSelection"}]}, "submenus": [{"id": "JoyCode.ai.chat.context", "group": "AJoyCoder", "label": "JoyCode: AI助手"}, {"id": "JoyCode.badJs.context", "group": "AJoyCoder", "label": "JoyCode: 查看BadJs", "when": "when.JoyCode.quickJump.searchH5BadJs || when.JoyCode.quickJump.searchXcxBadJs"}, {"id": "JoyCode.code.detection", "group": "AJoyCoder", "label": "JoyCode: 代码扫描"}, {"id": "JoyCode.createProject.start", "group": "AJoyCoder", "label": "JoyCode: 快速创建"}, {"id": "JoyCode.code.review", "group": "AJoyCoder", "label": "JoyCode: 代码评审"}, {"id": "JoyCode.code.AgentDriven", "group": "AJoyCoder", "label": "JoyCode: 自动化编程"}], "configuration": {"type": "object", "title": "JoyCode", "properties": {"JoyCode.enableBrowserTool": {"type": "boolean", "default": true, "description": "开启浏览器工具。"}, "JoyCode.enableCodebase": {"type": "boolean", "default": true, "description": "开启Codebase代码索引功能。关闭此选项后JoyCode将无法识别处理@codebase，除非严重影响性能，否则不要关闭此选项"}, "JoyCode.enableCheckpoints": {"type": "boolean", "default": true, "description": "允许扩展在任务进行过程中保存工作区的检查点。底层使用git，这可能不太适用于大型工作区。"}, "JoyCode.coder.selectFileNubmer": {"type": "number", "default": 3000, "minimum": 1000, "enum": [1000, 1500, 2000, 2500, 3000, 4000, 6000, 8000, 10000], "markdownDescription": "Coder初始化时读取的最大文件数，和@文件相关，数字越大初始化越慢，要结合当前项目需求文件数设定"}, "JoyCode.config.onlyTranslationFlutter3": {"type": "boolean", "markdownDescription": "只展示”转为flutter3“（批量翻译）", "default": false}, "JoyCode.config.workSpaceId": {"type": "string", "default": "joycoderfe", "markdownDescription": "工作区标识，不同工作区下存在不同功能和物料，默认为JoyCode官方工作区，[点此](https://dripworks-17tni50x-pro.local-pf.jd.com/#/)查看或新增您的工作区"}, "JoyCode.config.personalSnippets": {"type": "string", "editPresentation": "multilineText", "default": "{\"Snippet Sample\":{\"prefix\":\"joycode\",\"body\":[\"Hello JoyCode!\"],\"description\":\"JoyCode Default Snippet\"}}", "markdownDescription": "私有代码片段，可通过[生成工具](https://snippet-generator.app/)生成"}, "JoyCode.config.snippets.switch": {"type": "boolean", "markdownDescription": "是否开启代码片段(code snippet)索引填充功能", "default": true}, "JoyCode.config.chatModel": {"type": "string", "markdownDescription": "调用ChatGPT API时模型版本"}, "JoyCode.config.searchApiKey": {"type": "object", "default": {"serpApiKey": "", "googleApiKey": "", "googleCSEId": "", "tavilyApiKey": ""}, "markdownDescription": "使用联网搜索时使用的APIKEY和Search engine ID", "properties": {"serpApiKey": {"type": "string", "description": "使用SerpAPI联网搜索时使用的APIKEY，在 https://serpapi.com/ 申请"}, "googleApiKey": {"type": "string", "description": "在 https://console.cloud.google.com/apis/credentials 申请"}, "googleCSEId": {"type": "string", "description": "在 https://programmablesearchengine.google.com/controlpanel/create 申请"}, "tavilyApiKey": {"type": "string", "description": "在 https://app.tavily.com/home 申请"}}}, "JoyCode.config.codeCompletionsMaxLines": {"type": "number", "default": 1, "markdownDescription": "代码预测续写最大行数"}, "JoyCode.config.completionsRejectTimes": {"type": "number", "default": 200, "minimum": 50, "markdownDescription": "连续不采纳次数超过设置次数关闭预测功能"}, "JoyCode.config.translationRows": {"type": "number", "default": 200, "markdownDescription": "批量操作最大转换的行数"}, "JoyCode.config.translationTokens": {"type": "number", "default": 2000, "markdownDescription": "批量操作最大转换的token数"}, "JoyCode.config.codeCompletionsMaxTimes": {"type": "number", "default": 2000, "markdownDescription": "预测补全续写超时时间，默认2000毫秒"}, "JoyCode.config.codeCompletionsMoreContext": {"type": "boolean", "markdownDescription": "启用跨文件感知，识别同目录及打开的相似文件", "default": false}, "JoyCode.config.codeCompletionsFormat": {"type": "boolean", "markdownDescription": "启用代码补全格式化", "default": false}, "JoyCode.config.codeCompletionsGenTask": {"type": "string", "default": "LINE", "enum": ["LINE", "BLOCK", "FUNCTION", "TIME_OUT"], "enumItemLabels": ["整行优先", "代码块模式", "函数优先", "速度优先"], "enumDescriptions": ["按行生成代码，效果好，适用广，速度快", "优先生成代码块，速度稍慢", "生成函数级代码，速度较慢", "根据超时时间生成代码，速度取决于设置超时时间"], "markdownDescription": "设置JoyCode代码预测生成场景"}, "JoyCode.config.customHelper": {"type": "array", "default": [{"name": "正则表达式专家", "profile": "你是一位专门从事正则表达式的专家助理，你的主要任务是创建、解释和测试正则表达式模式。对于每一个正则表达式查询，你会详细解释表达式的每个部分，并总结整个正则表达式的目的。你还会提供几个测试用例，以涵盖潜在的边缘情况。你将积极编写和执行代码来验证这些测试用例，以确保全面理解和可靠应用正则表达式模式。你专注于与正则表达式相关的主题，避免进行非编程讨论。在澄清细节时，你会提出有针对性的问题，以收集基本信息，包括请求或生成样本文本，以有效地测试正则表达式模式。这种方法确保对正则表达式的全面而实用的理解，并通过实际应用和测试得到支持。"}, {"name": "SQL语句专家", "profile": "你是一位SQL专家，你会根据用户给出的概念模型，创建专业、高性能的SQL语句。你的主要目标是帮助用户创建SQL语句，助其进行数据查询、数据库管理和数据分析。指导如何编写高效准确的SQL查询，并提供优化数据库性能的建议。生成SQL语句的场景只输出SQL语句即可，不需要任何解释。介绍SQL语句的场景要尽可能输出详细的解释。以Markdown格式输出，用中文回答。"}, {"name": "Linux命令专家", "profile": "你是一位精通Linux的专家，你的职责是理解用户的诉求，并给出实现该诉求所需的Linux命令，命令中的变量用{}包裹，如{filename}。优先输出你生成的Linux命令，然后追加几句简单的介绍。"}, {"name": "技术文章翻译专家", "profile": "你是一位精通简体中文的专业翻译，尤其擅长将专业学术论文或技术文章翻译成浅显易懂的科普文章。请你帮我将以下英文段落翻译成中文，风格与中文科普读物相似。规则：1、翻译时要准确传达原文的事实和背景。2、即使上意译也要保留原始段落格式，以及保留术语，例如 FLAC，JPEG 等。保留公司缩写，例如 Microsoft, Amazon, OpenAI 等。3、人名不翻译4、同时要保留引用的论文，例如 [20] 这样的引用。5、对于 Figure 和 Table，翻译的同时保留原有格式，例如：“Figure 1: ”翻译为“图 1: ”，“Table 1: ”翻译为：“表 1: ”。6、全角括号换成半角括号，并在左括号前面加半角空格，右括号后面加半角空格。7、输入格式为 Markdown 格式，输出格式也必须保留原始 Markdown 格式8、在翻译专业术语时，第一次出现时要在括号里面写上英文原文，例如：“生成式 AI (Generative AI)”，之后就可以只写中文了。"}], "markdownDescription": "个性助手相关配置，支持配置多个", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "定义ChatGPT的角色/名称，例如：正则表达式专家"}, "profile": {"type": "string", "description": "定义助理的信息以及对它的要求，例如：你是一位xx专家，你的职责是xxx。可参考：https://github.com/f/awesome-chatgpt-prompts"}}}}, "JoyCode.config.chatKnowledge.options": {"type": "object", "default": {"template": "", "scoreThreshold": 0.4, "topK": 5, "chunkConent": false, "chunkSize": 1000}, "markdownDescription": "调用ChatGPT向量检索知识库的配置项，可参考[知识库使用文档](https://joyspace.jd.com/pages/zdnhyNNfbyYjLNvFm3Ua?block=XyVZOI)", "properties": {"template": {"type": "string", "description": "拼接知识和问题的模板"}, "scoreThreshold": {"type": "number", "description": "检索的最大距离"}, "topK": {"type": "number", "description": "在满足scoreThreshold情况下，最多取几个"}, "chunkConent": {"type": "boolean", "description": "是否切分文档"}, "chunkSize": {"type": "number", "description": "触发切分文档的长度"}}}, "JoyCode.config.cssModuleTransformIgnorePrefix": {"type": ["string", "array"], "default": [], "description": "css转换为css module时要忽略的类名前缀"}, "JoyCode.config.duplicateCodeDetection": {"type": "object", "markdownDescription": "代码重复检测相关配置，详见[jscpd文档](https://github.com/kucherenko/jscpd/tree/master/packages/jscpd#options)", "default": {"minLines": 5, "maxLines": 1000, "minTokens": 30, "ignore": "**/*.json,**/*.min.js,**/*.map"}}, "JoyCode.config.shenYi.commit": {"type": "boolean", "markdownDescription": "是否开启神医安全助手增量扫描", "default": false}, "JoyCode.config.codeReview.commit": {"type": "boolean", "markdownDescription": "是否开启代码评审增量扫描（评审时使用chat模型）", "default": false}, "JoyCode.config.codeReview.immediate": {"type": "boolean", "markdownDescription": "是否开启主动代码评审（评审时使用chat模型）", "default": false}, "JoyCode.config.codeReview.errorLine": {"type": "boolean", "markdownDescription": "是否开启行间错误提示", "default": false}, "JoyCode.config.codeReview.fileNumber": {"type": "number", "markdownDescription": "代码评审增量扫描最大文件数", "default": 2}, "JoyCode.config.codeReview.commitMessageStyle": {"type": "string", "default": "GIT_SCHEMA", "enum": ["GIT_SCHEMA", "BRANCH_SCHEMA", "AUTO", "DIY"], "enumItemLabels": ["标准模式", "分支模式", "变更摘要"], "enumDescriptions": ["根据 Conventional Commits 规范生成；\n示例：fix(chat): 接口错误", "生成变更摘要 + 分支名称;\n示例：fix: 接口错误[分支名称]", "生成变更摘要；\n示例：fix connection error"], "markdownDescription": "JoyCode生成Commit Message配置"}, "JoyCode.config.codeReview.otherRules": {"type": "string", "editPresentation": "multilineText", "markdownDescription": "代码评审自定义团队代码评审规范"}, "JoyCode.config.codeLens-row-menus": {"type": "boolean", "markdownDescription": "是否启用行间菜单", "default": true}, "JoyCode.config.test-llm-list": {"type": "string", "editPresentation": "multilineText", "default": "[]", "markdownDescription": "大模型配置列表"}, "JoyCode.config.codeTranslatePrompt": {"type": "string", "editPresentation": "multilineText", "default": "", "markdownDescription": "代码翻译自定义提示词"}, "JoyCode.config.autoActivateOnNewWindow": {"type": "boolean", "default": true, "description": "打开新窗口时自动激活JoyCode"}, "JoyCode.switch.AgentView": {"type": "boolean", "default": true, "markdownDescription": "旧版Chat和新版Agent切换"}, "JoyCode.coder.maxReadFileLine": {"type": "number", "default": 1000, "markdownDescription": "JoyCode默认读取文件的最大行数，默认1000行，设置为-1则每次均读取文件全部内容"}}}, "keybindings": [{"command": "joycoder.switch.Coder", "key": "ctrl+u", "mac": "cmd+u"}, {"command": "JoyCode.ai.log.reportImmediate", "key": "shift+ctrl+u", "mac": "shift+cmd+u"}, {"command": "JoyCode.uploadImage", "key": "shift+alt+p", "mac": "shift+alt+p", "when": "editorTextFocus"}, {"command": "JoyCode.pasteAndUploadImage", "key": "shift+alt+v", "mac": "shift+alt+v", "when": "editorTextFocus"}, {"command": "JoyCode.quickConsole", "key": "shift+alt+c", "mac": "shift+alt+c", "when": "editorTextFocus"}, {"command": "JoyCode.quickJump.search", "key": "shift+alt+s", "mac": "shift+alt+s", "when": "editorTextFocus"}, {"command": "JoyCode.ai.chat.other", "key": "shift+alt+i", "mac": "shift+alt+i", "when": "editorTextFocus"}, {"command": "JoyCode.ai.inlineChat.create", "key": "shift+alt+k", "mac": "shift+alt+k", "when": "editorTextFocus"}, {"command": "JoyCode.ai.inlineChat.create", "key": "ctrl+l", "mac": "cmd+l", "when": "editorTextFocus"}, {"command": "JoyCode.ai.inlineChat.delete", "key": "escape", "when": "when.JoyCode.RunEsc"}, {"command": "JoyCode.code-completions", "key": "shift+alt+enter", "when": "editorTextFocus"}, {"command": "editor.action.inlineSuggest.commit", "key": "Tab", "when": "inlineSuggestionVisible && !editorHoverFocused && !editorTabMovesFocus && editorTextFocus &&!editorR<PERSON>only"}, {"command": "editor.action.inlineSuggest.hide", "key": "Esc", "when": "editorTextFocus && inlineSuggestionVisible"}, {"command": "editor.action.inlineSuggest.showNext", "key": "shift+alt+down", "mac": "shift+alt+down", "when": "editorTextFocus && inlineSuggestionVisible"}, {"command": "editor.action.inlineSuggest.showPrevious", "key": "shift+alt+up", "mac": "shift+alt+up", "when": "editorTextFocus && inlineSuggestionVisible"}, {"command": "editor.action.inlineSuggest.acceptNextLine", "mac": "shift+tab", "key": "shift+tab", "when": "inlineSuggestionVisible && !editorHoverFocused && !editorTabMovesFocus && editorFocus &&!editorR<PERSON><PERSON>ly"}, {"command": "editor.action.inlineSuggest.acceptNextWord", "mac": "alt+.", "key": "alt+.", "when": "inlineSuggestionVisible && !editorHoverFocused && !editorTabMovesFocus && editorFocus &&!editorR<PERSON><PERSON>ly"}, {"command": "editor.action.inlineSuggest.trigger", "key": "shift+alt+.", "mac": "shift+alt+.", "when": "editorTextFocus"}, {"command": "JoyCode.code-completion.manually", "key": "shift+alt+right", "mac": "shift+alt+right", "when": "editorTextFocus"}], "snippets": [{"language": "javascript", "path": "./snippets/snippets.js.json"}, {"language": "javascriptreact", "path": "./snippets/snippets.js.json"}, {"language": "typescript", "path": "./snippets/snippets.js.json"}, {"language": "typescriptreact", "path": "./snippets/snippets.js.json"}, {"language": "css", "path": "./snippets/snippets.css.json"}, {"language": "scss", "path": "./snippets/snippets.css.json"}, {"language": "html", "path": "./snippets/snippets.html.json"}, {"language": "vue", "path": "./snippets/snippets.js.json"}, {"language": "python", "path": "./snippets/snippets.python.json"}, {"language": "go", "path": "./snippets/snippets.go.json"}, {"language": "java", "path": "./snippets/snippets.java.json"}, {"language": "cpp", "path": "./snippets/snippets.cpp.json"}]}, "extensionPack": [], "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/types": "^7.20.2", "@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@jscpd/core": "^3.4.5", "@types/babel__generator": "^7.6.4", "@types/babel__traverse": "^7.18.2", "@types/cross-spawn": "^6.0.2", "@types/css": "0.0.33", "@types/decompress": "^4.2.4", "@types/fast-json-stable-stringify": "^2.1.0", "@types/fs-extra": "^9.0.13", "@types/glob": "^8.0.0", "@types/karma-chrome-launcher": "^3.1.3", "@types/lodash": "^4.14.188", "@types/lodash-es": "^4.17.6", "@types/mocha": "^10.0.0", "@types/node": "20.x", "@types/prettier": "^2.7.1", "@types/react": "^18.0.25", "@types/react-dom": "^18.0.8", "@types/vscode": "^1.74.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "@typescript-eslint/parser": "^5.42.1", "@vscode/test-electron": "^2.1.4", "@vscode/vsce": "^2.22.0", "babel-loader": "^8.x.x", "bundlesize2": "^0.0.31", "chalk": "4", "clean-webpack-plugin": "^4.0.0", "cross-env": "^7.0.3", "css-loader": "^6.7.1", "css-minimizer-webpack-plugin": "^4.2.2", "eslint": "^8.27.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "file-loader": "^6.2.0", "glob": "^8.0.3", "husky": "^8.0.1", "lint-staged": "^13.0.3", "mini-css-extract-plugin": "^2.7.0", "minimist": "^1.2.7", "mocha": "^10.1.0", "npm-run-all": "^4.1.5", "prettier": "^2.7.1", "progress-bar-webpack-plugin": "^2.1.0", "rimraf": "^3.0.2", "sass": "^1.77.8", "sass-loader": "^14.2.1", "speed-measure-webpack-plugin": "^1.5.0", "standard-version": "^9.5.0", "style-loader": "^3.3.1", "terser-webpack-plugin": "^5.3.9", "ts-loader": "^9.4.1", "typescript": "^5.5.3", "webpack": "^5.74.0", "webpack-bundle-analyzer": "^4.7.0", "webpack-cli": "^4.10.0", "webpack-merge": "^5.8.0"}, "dependencies": {"@ant-design/icons": "^4.7.0", "@babel/generator": "^7.20.2", "@babel/parser": "^7.20.2", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/preset-env": "^7.26.0", "@babel/traverse": "^7.20.1", "@base/url": "^1.55.4", "@chiragrupani/karma-chromium-edge-launcher": "^2.3.1", "@joycoder/agent-common": "workspace:*", "@joycoder/agent-driven": "workspace:*", "@joycoder/plugin-base-ai": "workspace:*", "@joycoder/plugin-base-ai-d2c": "workspace:*", "@joycoder/plugin-base-app-manager": "workspace:*", "@joycoder/plugin-base-browser": "workspace:*", "@joycoder/plugin-base-cli-factory": "workspace:*", "@joycoder/plugin-base-code-completion": "workspace:*", "@joycoder/plugin-base-code-detection": "workspace:*", "@joycoder/plugin-base-code-review": "workspace:*", "@joycoder/plugin-base-dynamic-snippets": "workspace:*", "@joycoder/plugin-base-hover-provider": "workspace:*", "@joycoder/plugin-base-import-auto-complete": "workspace:*", "@joycoder/plugin-base-material-import": "workspace:*", "@joycoder/plugin-base-quick-console": "workspace:*", "@joycoder/plugin-base-quick-jump": "workspace:*", "@joycoder/plugin-base-sec": "workspace:*", "@joycoder/plugin-base-style-helper": "workspace:*", "@joycoder/plugin-base-taro": "workspace:*", "@joycoder/plugin-base-tree-view": "workspace:*", "@joycoder/plugin-base-upload-image": "workspace:*", "@joycoder/plugin-custom-active": "workspace:*", "@joycoder/plugin-custom-condition-compile": "workspace:*", "@joycoder/plugin-custom-hover-provider": "workspace:*", "@joycoder/plugin-custom-quick-jump": "workspace:*", "@joycoder/shared": "workspace:^", "@joycoder/version": "workspace:*", "@joycoder/web": "workspace:*", "@test/joycoder-rag-mcp-server": "^1.0.76", "@uiw/react-watermark": "^1.0.0", "adm-zip": "^0.5.14", "antd": "^4.24.14", "await-to-js": "^3.0.0", "axios": "^1.2.1", "axios-jsonp": "^1.0.4", "axios-retry": "^3.3.1", "browser-viewport-device-descriptions": "^1.1.0", "cheerio": "1.0.0-rc.12", "comment-json": "^4.2.3", "compare-versions": "^5.0.1", "cookie": "^0.5.0", "copy-webpack-plugin": "^11.0.0", "cross-spawn": "^7.0.3", "css": "^3.0.0", "css-flatten": "^2.0.0", "decompress": "^4.2.1", "diff": "^5.2.0", "event-emitter-enhancer": "^2.0.0", "eventemitter2": "^6.4.9", "fast-glob": "^3.2.12", "fast-json-stable-stringify": "^2.1.0", "form-data": "^4.0.0", "fs-extra": "^10.1.0", "fuse.js": "^6.6.2", "get-tsconfig": "^4.4.0", "gpt-tokens": "^1.3.3", "html-entities": "^2.4.0", "html-to-text": "^9.0.5", "jscpd": "^3.4.5", "jsdom": "^21.1.1", "jsonfile": "^6.1.0", "karma-chrome-launcher": "^3.2.0", "langchain": "^0.1.21", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "md5-file": "^5.0.0", "moment": "^2.29.4", "node-fetch": "^3.3.0", "node-html-markdown": "^1.3.0", "openai": "^4.28.0", "pinyin-pro": "^3.26.0", "playwright-core": "^1.51.1", "postcss": "^8.4.21", "postcss-less": "^6.0.0", "postcss-scss": "^4.0.6", "re-resizable": "^6.9.11", "react": "^18.2.0", "react-dom": "^18.2.0", "react-draggable": "^4.4.5", "react-router-dom": "^6.4.3", "rehype-minify-whitespace": "^5.0.1", "rehype-raw": "^7.0.0", "sass-loader": "^14.2.1", "serialize-error": "^11.0.0", "simple-git": "^3.14.1", "strip-comments": "^2.0.1", "tar": "^7.4.0", "vue-template-compiler": "^2.7.14", "walkdir": "^0.4.1", "web-tree-sitter": "^0.22.6"}}