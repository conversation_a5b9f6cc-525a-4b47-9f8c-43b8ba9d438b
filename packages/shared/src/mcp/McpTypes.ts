export interface McpNodeService {
  serviceId: string;
  name: string;
  displayName?: string;
  description: string;
  uploader: string;
  tags: string[];
  categories: string[];
  updateTime: string;
  version: number;
  repository: string;
  stars: number | null;
  serverParam: string; // 考虑使用更具体的类型，如 ServerParam 接口
  depType: McpDeploymentType; // 考虑使用 DependencyType 枚举
  transType: McpTransportType; // 考虑使用 TransactionType 枚举
  ckTimes: number;
  myFavorite: boolean;
  favoriteCount: number;
  votes: any | null; // TODO: 定义具体的投票类型
  logoUrl: string | null;
  isPluginMcp: boolean;
  pluginId: string | null;
  expressSetup: boolean | null;
  installTimes: number | null;
  isProject?: boolean | null;
}

export enum McpSendMsgType {
  MCP_LIST = 'mcp-list', // mcp市场列表
  GET_MCP_INSTALL_PARAM = 'get-mcp-install-param', // 获取mcp安装参数 joyagent上的插件mcp
  OPEN_MCP_SETTING_FILE = 'open-mcp-setting-file', // 打开mcp全局设置文件
  GET_MCP_SETTINGS_FILE_CONTENT = 'get-mcp-settings-file-content', // 获取mcp全局设置文件内容
  UPDATE_OR_INSERT_MCP_SERVER = 'update-or-insert-mcp-server', // 更新或插入mcp服务器
  OPEN_OLD_MCP_SERVER_CONFIG_PAGE = 'open-old-mcp-server-config-page', // 打开右侧旧的mcp服务器配置页面
  REFRESH_MCP_SERVICE = 'refresh-mcp-service', // 刷新mcp服务
  GET_MCP_CONNECTION_SERVER = 'get-mcp-connection-server', // 获取mcp连接服务器
  TOGGLE_MCP_SERVER = 'toggle-mcp-server', // mcp服务器开关
  RESTART_MCP_SERVER = 'restart-mcp-server', // 重启mcp服务
  UPDATE_TOOL_AUTO_APPROVE = 'update-tool-auto-approve', // 更新工具自动确认状态
  DELETE_MCP_SERVER = 'delete-mcp-server', // 删除mcp服务
}

export enum McpCommonSendMsgType {
  SETTING_MCP = 'setting-mcp',
}

export enum McpTransportType {
  STDIO = 1,
  SSE = 2,
  STREAMABLE = 3,
}

export enum McpDeploymentType {
  LOCAL = 1,
  HOSTED = 2,
}

export function getTransportTypeString(type: McpTransportType): string {
  switch (type) {
    case McpTransportType.STDIO:
      return 'STDIO';
    case McpTransportType.SSE:
      return 'SSE';
    case McpTransportType.STREAMABLE:
      return 'STREAMABLE';
    default:
      return 'Unknown';
  }
}

export function getDeploymentTypeString(type: McpDeploymentType): string {
  switch (type) {
    case McpDeploymentType.LOCAL:
      return 'Local';
    case McpDeploymentType.HOSTED:
      return 'Hosted';
    default:
      return 'Unknown';
  }
}

export function getDeploymentTypeStr(type: number): string {
  switch (type) {
    case 1:
      return 'Local';
    case 2:
      return 'Hosted';
    default:
      return 'Unknown';
  }
}
