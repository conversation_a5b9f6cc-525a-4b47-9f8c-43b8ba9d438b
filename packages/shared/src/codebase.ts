import * as codebase from '@test/joycoder-rag-mcp-server/dist/vscode/adapter/codebase.js';
import * as vscode from 'vscode';
import { CodebaseIndexingStatus, CodebaseProgress, CodebaseProgressCallback } from './types/codebase';

export class CodebaseManager {
  private static stopCodebaseProgressCallback = false;
  private static context: vscode.ExtensionContext | undefined;
  static async initCodebase(context: vscode.ExtensionContext) {
    this.context = context;
    const enableCodebase = vscode.workspace.getConfiguration('JoyCode').get<boolean>('enableCodebase', false);
    const codebaseIndexingStatus =
      ((await this.context.workspaceState.get('codebaseIndexingStatus')) as CodebaseIndexingStatus) ??
      CodebaseIndexingStatus.UNINDEXED;

    if (enableCodebase && codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED) {
      await this.getProgress();
      console.log('[Codebase]二进制随插件启动，当前仓库状态：已索引');
    } else {
      console.log(
        '[Codebase]二进制不随插件启动，原因：当前仓库未索引 | 索引意外退出 | 未开启Codebase索引功能',
        codebaseIndexingStatus,
        enableCodebase
      );
    }
    if (codebaseIndexingStatus === CodebaseIndexingStatus.INDEXING) {
      console.log('[Codebase] 上一次索引中发生意外错误，修正索引状态为UNIDEXED');
      await this.context.workspaceState.update('codebaseIndexingStatus', CodebaseIndexingStatus.UNINDEXED);
    }
    //由于服务刚启动时获取的不准，所以注释
    //await vscode.workspace.getConfiguration('JoyCode').update('codebaseIndexingStatus', codebaseProgress.status);
  }

  static convertProgressInfoToCodebaseProgress(progressInfo: codebase.ProgressInfo): CodebaseProgress {
    let status = CodebaseIndexingStatus.UNINDEXED;
    if (progressInfo.isIndexing) {
      status = CodebaseIndexingStatus.INDEXING;
    }
    if (progressInfo.progress === 100) {
      status = CodebaseIndexingStatus.INDEXED;
    }
    return {
      progress: progressInfo.progress,
      status,
    };
  }

  static async getProgress() {
    try {
      const progressInfo = await codebase.getProgress();
      console.log(progressInfo);
      return this.convertProgressInfoToCodebaseProgress(progressInfo);
    } catch (e) {
      // console.log('[Codebase]getProgress出错，无工作目录', e);
      return {
        progress: 0,
        status: CodebaseIndexingStatus.NO_WORKSPACE,
      };
    }
  }

  /**
   * 获取codebase索引进度
   * @param callback 索引进度变化回调函数
   */
  static async getProgressCallback(callback?: CodebaseProgressCallback) {
    const updateProgress = async (codebaseProgress: CodebaseProgress) => {
      if (this.stopCodebaseProgressCallback) return;
      callback?.onProgressChange?.(codebaseProgress);
      if (callback?.onComplete && codebaseProgress.status === CodebaseIndexingStatus.INDEXED) {
        // await vscode.workspace
        //   .getConfiguration('JoyCode')
        //   .update('codebaseIndexingStatus', CodebaseIndexingStatus.INDEXED);
        await this.context?.workspaceState.update('codebaseIndexingStatus', CodebaseIndexingStatus.INDEXED);
        callback.onComplete(codebaseProgress);
      }
    };

    try {
      const progressInfo = await codebase.getProgress({
        onProgressChange(newProgressInfo) {
          const codebaseProgress = CodebaseManager.convertProgressInfoToCodebaseProgress(newProgressInfo);
          updateProgress(codebaseProgress);
        },
      });
      updateProgress(this.convertProgressInfoToCodebaseProgress(progressInfo));
    } catch (e) {
      const errorProgress = {
        progress: 0,
        status: CodebaseIndexingStatus.NO_WORKSPACE,
      };
      updateProgress(errorProgress);
    }
    return;
  }

  static getLocalIndexingStatus() {
    // const codebaseIndexingStatus = vscode.workspace
    //   .getConfiguration('JoyCode')
    //   .get<CodebaseIndexingStatus>('codebaseIndexingStatus', CodebaseIndexingStatus.UNINDEXED);
    const codebaseIndexingStatus =
      (this.context?.workspaceState.get('codebaseIndexingStatus') as CodebaseIndexingStatus) ??
      CodebaseIndexingStatus.UNINDEXED;
    return {
      progress: codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED ? 100 : 0,
      status: codebaseIndexingStatus,
    };
  }

  static async cancelIndex() {
    this.stopCodebaseProgressCallback = true;
    await codebase.cancelIndex();
    // await vscode.workspace
    //   .getConfiguration('JoyCode')
    //   .update('codebaseIndexingStatus', CodebaseIndexingStatus.UNINDEXED);
    await this.context?.workspaceState.update('codebaseIndexingStatus', CodebaseIndexingStatus.UNINDEXED);
  }

  static async startIndex() {
    this.stopCodebaseProgressCallback = false;
    // await vscode.workspace
    //   .getConfiguration('JoyCode')
    //   .update('codebaseIndexingStatus', CodebaseIndexingStatus.INDEXING);
    await this.context?.workspaceState.update('codebaseIndexingStatus', CodebaseIndexingStatus.INDEXING);
    await codebase.startIndex();
    // this.getProgressCallback(callback);
  }

  static async removeIndex() {
    // await vscode.workspace
    //   .getConfiguration('JoyCode')
    //   .update('codebaseIndexingStatus', CodebaseIndexingStatus.UNINDEXED);
    await this.context?.workspaceState.update('codebaseIndexingStatus', CodebaseIndexingStatus.UNINDEXED);
    await codebase.removeIndex();
  }

  static async codebaseSearch(query?: string) {
    const semanticContext = await codebase.getSemanticContext(query || '');
    // console.log('[Codebase]查询========>query', query);
    // console.log('[Codebase]查询========>semanticContext.files', semanticContext.files);
    return semanticContext;
  }
}
