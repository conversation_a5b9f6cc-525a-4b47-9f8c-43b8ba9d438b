export enum CodebaseIndexingStatus {
  INDEXING = 'INDEXING',
  INDEXED = 'INDEXED',
  UNINDEXED = 'UNINDEXED',
  PREPARING = 'PREPARING',
  NO_WORKSPACE = 'NO_WORKSPACE',
}
export interface CodebaseProgress {
  progress: number;
  status: CodebaseIndexingStatus;
}
export interface CodebaseProgressCallback {
  onProgressChange?: (progress: CodebaseProgress) => void;
  onComplete?: (progress?: CodebaseProgress) => void;
}
