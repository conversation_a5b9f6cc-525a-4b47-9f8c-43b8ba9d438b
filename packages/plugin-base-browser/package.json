{"name": "@joycoder/plugin-base-browser", "version": "3.0.0", "description": "内置H5开发调试模块 - 基于playwright", "author": "JoyCoder", "homepage": "https://coding.jd.com/JoyCoder-IDE/JoyCoder-Plugin.git", "private": true, "license": "MIT", "main": "src/index.ts", "module": "src/index.ts", "files": ["src"], "repository": {"type": "git", "url": "https://coding.jd.com/JoyCoder-IDE/JoyCoder-Plugin.git"}, "scripts": {}, "bugs": {"url": "https://coding.jd.com/JoyCoder-IDE/JoyCoder-Plugin/issues/"}, "publishConfig": {"access": "public"}, "dependencies": {"@joycoder/shared": "workspace:*", "@joycoder/web": "workspace:*"}}