// 库
import * as path from 'path';
import * as vscode from 'vscode';
import { EventEmitter2 } from 'eventemitter2';
// 业务库
import {
  getAllTabs,
  getAssetsFilePath,
  openInBrowser,
  GlobalState,
  openInVscodeWithBrowser,
  getZoomRatio,
} from '@joycoder/shared';
import BrowserPage from '../browser/BrowserPage';
import ContentProvider from '../provider/ContentProvider';
// 业务定义
import { IMessageRequestData, IMessageResponseData } from '../shared/interface';

/**
 * vscode的 webview 窗口操作类
 */
export default class WebviewPanel extends EventEmitter2 {
  private _panel: vscode.WebviewPanel | null;

  private browserPage: BrowserPage | null;

  private disposables: vscode.Disposable[] = [];

  constructor() {
    super();
    this._panel = null;
    this.browserPage = null;
  }

  /**
   * 新建新的 vscode webview 窗口
   */
  public async launch(browserPage: BrowserPage, startUrl: string, panels: any) {
    try {
      this.browserPage = browserPage;
      // 这里其实就是监听除on之外的所有消息（消息来源主要是 cdp 发送过来的）
      // @ts-ignore
      this.browserPage.else((event: string, message: IMessageResponseData) => {
        // 如果消息体不是 cdp 传递过来或者 webview 面板已经关闭了则直接忽略
        if (!event.startsWith('cdp.') || !this._panel) {
          return;
        }
        // 向webview发送消息
        this._panel.webview.postMessage(message);
      });
    } catch (error: any) {
      vscode.window.showErrorMessage(error.message);
    }

    // 用于判断窗口如何展示
    const tabs = getAllTabs();
    const oneViewColumnPanels = [...panels].filter((item) => {
      return item._panel?.viewColumn == vscode.ViewColumn.One;
    });
    const isDebugUrl = startUrl.includes('devtools/inspector.html');
    const { groups = [] } = (await vscode.commands.executeCommand('vscode.getEditorLayout')) as any;

    // 打开调试时先将分栏调整为1:1，否则后面设置三栏比例时会异常
    if (isDebugUrl && groups.length == 2) {
      await vscode.commands.executeCommand('vscode.setEditorLayout', {
        orientation: 0,
        groups: [{ size: 0.5 }, { size: 0.5 }],
      });
    }
    // 新增一个vscode的窗口
    this._panel = vscode.window.createWebviewPanel(
      'joycoder-browser', // viewType
      'JoyCode Browser', // title
      {
        // debug窗口分三列
        // 没有打开的Tab或者已打开的webview没有分割，则后面打开的也不分割
        viewColumn: isDebugUrl
          ? vscode.ViewColumn.Three
          : tabs.length == 0 || oneViewColumnPanels.length > 0
          ? vscode.ViewColumn.One
          : vscode.ViewColumn.Two,
        preserveFocus: true,
      },
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        // 设置 vscode-resource: 允许加载的本地资源根目录
        localResourceRoots: [vscode.Uri.file(path.join(__dirname, '/web'))],
      }
    );
    // 打开调试时设置分栏比例
    // groups.length == 2 实际已经是3，因为上面又新开了一个
    if (isDebugUrl && groups.length == 2) {
      await vscode.commands.executeCommand('vscode.setEditorLayout', {
        orientation: 0,
        groups: [{ size: 0.5 }, { size: 0.25 }, { size: 0.25 }],
      });
    }
    // 第一个打开的分割窗口，设置webview宽度比例为35%
    if (tabs.length > 0 && panels.size == 1) {
      vscode.commands.executeCommand(
        'vscode.setEditorLayout',
        vscode.window.activeTextEditor
          ? { orientation: 0, groups: [{ size: 0.65 }, { size: 0.35 }] }
          : { orientation: 0, groups: [{}] }
      );
    }

    // 加载 webview 的内容
    this._panel.webview.html = ContentProvider.getContent(this._panel, startUrl);
    // 设置标签栏图标
    this._panel.iconPath = getAssetsFilePath('logo.png');
    // 监听webview窗口销毁事件
    // onDidDispose会生成一个 vscode.Disposable 对象，同时会加入到声明的 this.disposables 中
    this._panel.onDidDispose(() => this.dispose(), null, this.disposables);
    // 监听webview可视状态切换事件
    this._panel.onDidChangeViewState(
      (e) => {
        // 发出消息（在 WebviewPanelManager 中 create 有监听）
        this.emit((this._panel as vscode.WebviewPanel).active ? 'focus' : 'blur');
      },
      null,
      this.disposables
    );
    // 监听 webview 代码主动传递的消息
    this._panel.webview.onDidReceiveMessage(
      (message: IMessageRequestData) => {
        // 不是html主动发起的消息则直接忽略
        if (!message.type.startsWith('client.')) {
          return;
        }
        // 其他场景需要直接将消息转发给browserPage
        if (!this.browserPage) {
          return;
        }
        if (message.type === 'client.updateTitle') {
          const title = message.params.title as string;
          // 更新标题
          if (this._panel) {
            this._panel.title = title;
          }
          return;
        }
        if (message.type === 'client.openWithBrowse') {
          const url = message.params.url as string;
          // 用系统浏览器打开url
          openInBrowser(url);
          return;
        }
        if (message.type === 'client.createDebugPanel') {
          this.createDebugPanel();
          return;
        }
        // webview加载回调，此时可以postmessage给H5页面
        if (message.type == 'client.webview.loaded') {
          // 监听vscode缩放比例（cmd + 加号），调整webview的zoom值，以便得到一个正确的375尺寸，否则会被放大且模糊
          vscode.workspace.onDidChangeConfiguration((event) => {
            if (event.affectsConfiguration('window.zoomLevel')) {
              this._panel?.webview.postMessage({
                type: 'vscode.zoomLevelChanged',
                data: getZoomRatio(),
              });
            }
          });
          // 同步设置信息到webview
          this._panel?.webview.postMessage({
            type: 'vscode.webview.setting',
            data: GlobalState.get('JoyCoderBrowserConfig') || {},
          });
          return;
        }
        // 代理等设置内容
        if (message.type == 'client.webview.setting') {
          GlobalState.update('JoyCoderBrowserConfig', message.params);

          // 重启以使配置生效
          this.dispose();
          vscode.commands.executeCommand('JoyCode.browser.open', message.params.currentUrl);
          return;
        }
        try {
          this.browserPage.send(message);
        } catch (error: any) {
          vscode.window.showErrorMessage(error.message);
        }
      },
      null,
      this.disposables
    );

    // 发送聚焦消息（在 WebviewPanelManager 中 create 有监听）
    this.emit('focus');
  }

  public async createDebugPanel() {
    if (!this.browserPage) return;
    const domain = `localhost:9222`;
    // @ts-ignore
    // 从puppeteer切换为playwright后，无法再通过这种方式获得pageId打开开发者工具，
    // 需要通过
    const pageId = this.browserPage.getPageId();
    const debugUrl = `http://${domain}/devtools/inspector.html?ws=${domain}/devtools/page/${pageId}&experiments=true`;
    openInVscodeWithBrowser(debugUrl);
  }

  /**
   * 销毁处理函数
   */
  public dispose() {
    // 销毁 webview 窗口
    if (this._panel) {
      this._panel.dispose();
    }
    // 销毁无头浏览器的页面对象
    if (this.browserPage) {
      this.browserPage.dispose();
      this.browserPage = null;
    }
    // 销毁因监听事件产生的所有 Disposable
    while (this.disposables.length) {
      const item = this.disposables.pop();
      item && item.dispose();
    }
    // 发送 disposed 消息（在 WebviewPanelManager 中 create 有监听）
    this.emit('disposed');
    // 移除所有监听的消息
    this.removeAllListeners();
  }
}
