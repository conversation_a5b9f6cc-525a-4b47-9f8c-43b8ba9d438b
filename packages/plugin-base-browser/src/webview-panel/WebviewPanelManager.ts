// 库
import * as vscode from 'vscode';
// 业务库
import WebviewPanel from './WebviewPanel';
import BrowserClient from '../browser/BrowserClient';
// 业务常量
import { DEFAULT_START_URL } from '../shared/constant';
// 本地定义
type IWindowOpenParam = {
  url: string;
};

/**
 * vscode的 webview 窗口面板管理类
 */
export default class WebviewPanelManager {
  // eslint-disable-next-line no-use-before-define
  public static instance: WebviewPanelManager;

  public readonly context: vscode.ExtensionContext; // vscode插件的上下文对象

  private panels: Set<WebviewPanel>; // 生成的所有webview窗口对象

  private current: WebviewPanel | null = null; // 当前聚焦的面板

  private browserClient: BrowserClient | null = null; // 当前 webview 窗口对应的 playwright无头浏览器 实例

  constructor(context: vscode.ExtensionContext) {
    // 属性初始化
    this.panels = new Set();
    this.context = context;
    // 初始化实例对象
    WebviewPanelManager.instance = this;
  }

  /**
   * 新创建一个vscode的webview窗口
   */
  public async create(startUrl = DEFAULT_START_URL) {
    // 如果当前还没有创建浏览器，则新建一个浏览器对象
    if (!this.browserClient) {
      this.browserClient = new BrowserClient();
    }
    // 生成一个面板对象
    const panel = new WebviewPanel();
    // 监听 disposed 事件（销毁事件）
    panel.once('disposed', () => {
      // 清理对象
      if (this.current === panel) {
        this.current = null;
      }
      // 删除存储的面板对象
      this.panels.delete(panel);
      // 如果没有任何面板了则销毁无头浏览器
      if (this.panels.size === 0) {
        (this.browserClient as BrowserClient).dispose();
        this.browserClient = null;
      }
    });
    // 监听 windowOpenRequested 消息（无头浏览器内新开窗口的场景），并同步发出对应消息
    panel.on('windowOpenRequested', (params: IWindowOpenParam) => {
      this.create();
    });
    // 监听焦点事件
    panel.on('focus', () => {
      this.current = panel;
    });
    // 监听失去焦点事件，确保 current 对应的是聚焦的 vscode webview 窗口
    panel.on('blur', () => {
      if (this.current === panel) {
        this.current = null;
      }
    });
    // 将面板添加到缓存中
    this.panels.add(panel);
    // 启动浏览器
    const browserPage = await this.browserClient.createBrowserPage();
    // 启动浏览器和创建新的vscode窗口
    await panel.launch(browserPage, startUrl, this.panels);
    // 向 vscode 的上下文中追加销毁处理事件，如果插件销毁了则会触发这个销毁事件
    this.context.subscriptions.push({
      // 这里 panel 的 dispose 函数里会抛出一个 disposed 消息触发上面监听的消息逻辑
      dispose: () => panel.dispose(),
    });
    return {
      panel,
      browserPage,
    };
  }
}
