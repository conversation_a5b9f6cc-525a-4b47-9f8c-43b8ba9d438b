// 库
import * as vscode from 'vscode';

/**
 * 通用工具类
 */
export default class CommonTool {
  /**
   * 判断是否是暗黑模式
   */
  static isDarkTheme() {
    const theme = CommonTool.getConfig('workbench.colorTheme', '').toLowerCase();
    // must be dark
    if (theme.match(/dark|black/i) != null) {
      return true;
    }
    // must be light
    if (theme.match(/light/i) != null) {
      return false;
    }
    // IDK, maybe dark
    return true;
  }

  // --------------------私有函数--------------------
  /**
   * 获取单个插件配置项目
   */
  private static getConfig<T>(key: string, defaultValue: T) {
    return vscode.workspace.getConfiguration().get(key, defaultValue);
  }
}
