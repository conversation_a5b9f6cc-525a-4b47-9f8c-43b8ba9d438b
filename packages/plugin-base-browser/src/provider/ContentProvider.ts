// 库
import * as path from 'path';
import * as vscode from 'vscode';
import { DEFAULT_START_URL } from '../shared/constant';
import { THEME_HTML } from '@joycoder/web/src/constants/html';
import { getZoomRatio, isDebug } from '@joycoder/shared';

export default class ContentProvider {
  /**
   * 获取 webview 的 html 内容
   */
  public static getContent(panel: vscode.WebviewPanel, startUrl = DEFAULT_START_URL) {
    // 公共 js
    const vendorScriptPath = panel.webview.asWebviewUri(
      vscode.Uri.file(path.join(__dirname, '/web/vendors/vendors.js'))
    );
    // 业务 js
    const bundleScriptPath = panel.webview.asWebviewUri(
      vscode.Uri.file(path.join(__dirname, '/web/browseLite/bundle.js'))
    );
    const vendorCssPath = panel.webview.asWebviewUri(vscode.Uri.file(path.join(__dirname, '/web/vendors/vendors.css')));
    const bundleCssPath = panel.webview.asWebviewUri(
      vscode.Uri.file(path.join(__dirname, '/web/browseLite/bundle.css'))
    );
    // 返回html内容
    return `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
          ${THEME_HTML}
          ${
            !isDebug()
              ? `
          <link rel="stylesheet" type="text/css" href="${vendorCssPath}"}>
          <link rel="stylesheet" type="text/css" href="${bundleCssPath}"}>
          `
              : ''
          }
        </head>
        <body>
          <div id="root"></div>
          <script>
          window.joyCoderStartUrl = "${startUrl}";
          window.joyCoderZoomRatio = ${getZoomRatio()};
          </script>
          <script src="${vendorScriptPath}"></script>
          <script src="${bundleScriptPath}"></script>
        </body>
      </html>
    `;
  }
}
