{"name": "@joycoder/plugin-base-code-review", "version": "3.0.0", "description": "", "author": "JoyCoder", "homepage": "https://coding.jd.com/JoyCoder/JoyCoder-VSCode.git", "private": true, "main": "src/index.ts", "module": "src/index.ts", "files": ["src"], "repository": {"type": "git", "url": "https://coding.jd.com/JoyCoder/JoyCoder-VSCode.git"}, "scripts": {"lint": "eslint src --ext ts", "tsc": "tsc --noEmit --allowJs --esModuleInterop --resolveJsonModule --skipLibCheck"}, "bugs": {"url": "https://coding.jd.com/JoyCoder/JoyCoder/issues/"}, "publishConfig": {"access": "public"}, "dependencies": {"@joycoder/plugin-base-ai": "workspace:*", "@joycoder/shared": "workspace:*", "@joycoder/plugin-base-sec": "workspace:*", "web-tree-sitter": "^0.22.6", "uuid": "^9.0.1"}}