import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import {
  ActionType,
  getRepoName,
  getVscodeConfig,
  Logger,
  queryRemoteIHubConfigSync,
  reportAction,
} from '@joycoder/shared';
import { getIsInWhiteList } from '@joycoder/plugin-base-sec';
import { getCodeReviewData } from '../utils/getCodeReviewData';
import { defaultIgnoreFile, defaultIgnoreFileList } from '@joycoder/shared';
import { debounce } from 'lodash';

let lastCommitHash = '';
export async function loadIhubRules(): Promise<Record<string, string>[]> {
  // 获取当前工作区目录
  const rules = await queryRemoteIHubConfigSync();
  const { ihub } = rules;
  return ihub;
}
export function isIHubFn() {
  if (!vscode.workspace.workspaceFolders) {
    return false;
  }
  try {
    const fsPath = vscode.workspace.workspaceFolders[0].uri.fsPath;
    const ifloorFilePath = path.join(fsPath, './ifloor.config.js');
    const configFilePath = path.join(fsPath, './config.json');
    if (!fs.existsSync(configFilePath) && !fs.existsSync(ifloorFilePath)) {
      return false;
    }
    const content = fs.readFileSync(configFilePath, 'utf8');
    const ifloor = fs.readFileSync(ifloorFilePath, 'utf8');
    return !!content && !!ifloor;
  } catch (error) {
    return false;
  }
}

export async function checkIHubRules(context: vscode.ExtensionContext) {
  const ihubRules = await loadIhubRules();
  const isChecked = getVscodeConfig('JoyCode.config.codeReview.commit');
  const isInWhiteList = await getIsInWhiteList();
  const isIHub = isIHubFn();
  Logger.log(
    '%c [ 校验是否为通天塔 ]-42',
    'font-size:13px; background:pink; color:#bf2c9f;',
    !isIHub || !ihubRules || ihubRules.length === 0 || !isChecked || !isInWhiteList
  );
  if (!isIHub || !ihubRules || ihubRules.length === 0 || !isChecked || !isInWhiteList) {
    return;
  }
  const gitExtension = vscode.extensions.getExtension('vscode.git');
  const git = await gitExtension?.activate?.();
  const api = git?.getAPI?.(1);
  if (api) {
    api.onDidOpenRepository((repository) => {
      monitorRepository(repository, ihubRules);
    });
    api.repositories.forEach((repository) => {
      monitorRepository(repository, ihubRules);
    });
  }
}

function monitorRepository(repository: any, ihubRules: Record<string, string>[]) {
  repository.state.onDidChange(
    debounce(
      async () => {
        try {
          const currentCommitHash = repository.state.HEAD?.commit;
          if (currentCommitHash === lastCommitHash) {
            return;
          }
          lastCommitHash = currentCommitHash;
          Logger.log(
            '%c [ currentCommitHash ]-72',
            'font-size:13px; background:pink; color:#bf2c9f;',
            currentCommitHash
          );
          const changes = repository.state.workingTreeChanges;
          Logger.log('%c [ 修改GitTree ]-78', 'font-size:13px; background:pink; color:#bf2c9f;', changes);
          if (changes?.length > 0) {
            let checkResults: any = {};
            const changeFiles = changes.map(async (change: any) => {
              const { uri, status } = change;
              const gitStatus = getGitStautusName(status);
              const filePath = uri.fsPath;
              const fileName = filePath.split('/').pop(); // 获取文件名称
              try {
                const fileExtension = fileName.split('.').pop(); // 获取文件后缀名
                const isIgnoreFile = defaultIgnoreFile.ignores(fileName) || defaultIgnoreFileList.includes(fileName);
                if (isIgnoreFile) {
                  return {
                    filePath,
                    gitStatus,
                    checkResult: '',
                    fileName,
                  };
                }
                const fileContentBuffer = await vscode.workspace.fs.readFile(uri);
                const fileContent = new TextDecoder().decode(fileContentBuffer);
                checkResults = await getCodeReviewData(fileContent, fileExtension, true, ihubRules);
                Logger.log(
                  '%c [ 模型返回内容checkResults ]-99',
                  'font-size:13px; background:pink; color:#bf2c9f;',
                  checkResults
                );

                return {
                  filePath,
                  gitStatus,
                  checkResult: checkResults.crResults,
                  fileName,
                };
              } catch (error) {
                return {
                  filePath,
                  checkResult: '',
                  gitStatus: '',
                  fileName,
                };
              }
            });
            const changeList = await Promise.all(changeFiles);
            if (vscode.workspace.workspaceFolders) {
              const fsPath = vscode.workspace.workspaceFolders[0].uri.fsPath;
              const rcFilePath = path.join(fsPath, './.joycoder.rc');
              const content = fs.existsSync(rcFilePath) ? fs.readFileSync(rcFilePath, 'utf8') : '{}';
              const existingContent = JSON.parse(content);
              const existingData = existingContent.ihub?.files || [];
              const reviewData = changeList.filter((item) => item.checkResult !== '');
              const mergedData = [...existingData, ...reviewData].reduce((accumulator, current) => {
                accumulator.set(current.filePath, current);
                return accumulator;
              }, new Map());
              const newReviewData = Array.from(mergedData.values());
              const rootUri = repository.rootUri;
              const remoteUrl = repository?.state?.remotes[0]?.fetchUrl;
              const repoName = await getRepoName(fsPath);
              Logger.log('%c [ repoName ]-137', 'font-size:13px; background:pink; color:#bf2c9f;', repoName);
              const result = {
                ihub: {
                  rootUri,
                  remoteUrl,
                  repoName,
                  commitHash: lastCommitHash,
                  files: newReviewData,
                },
              };
              // 写入该文件
              const rcContent = JSON.stringify(result, null, 2);
              Logger.log('%c [ 评审结果 ]-142', 'font-size:13px; background:pink; color:#bf2c9f;', rcContent);
              // 上报给后端
              reportAction({
                accept: 1,
                actionCate: 'ai',
                actionType: ActionType.IHub,
                conversationId: checkResults.crId,
                result: '',
                extendMsg: {
                  plugin_history: rcContent,
                },
              });
              fs.writeFileSync(rcFilePath, rcContent);
            }
          }
        } catch (error) {
          Logger.error('%c [ error ]-157', 'font-size:13px; background:pink; color:#bf2c9f;', error);
        }
      },
      20000,
      { leading: true, trailing: true }
    )
  );
}
function getGitStautusName(gitStatus: string | number) {
  const statusList = [
    {
      id: 1,
      name: 'MODIFIED',
      description: '文件已被修改但尚未暂存',
    },
    {
      id: 2,
      name: 'ADDED',
      description: '新文件已被添加到暂存区',
    },
    {
      id: 3,
      name: 'DELETED',
      description: '文件已从工作目录中删除',
    },
    {
      id: 4,
      name: 'RENAMED',
      description: '文件已被重命名',
    },
    {
      id: 5,
      name: 'COPIED',
      description: '文件已被复制',
    },
    {
      id: 6,
      name: 'UNMERGED',
      description: '文件有未解决的合并冲突',
    },
    {
      id: 7,
      name: 'IGNORED',
      description: '文件被Git忽略（通常在.gitignore文件中定义）',
    },
    {
      id: 8,
      name: 'UNTRACKED',
      description: '文件存在于工作目录中，但尚未被Git跟踪',
    },
    {
      id: 9,
      name: 'TYPECHANGE',
      description: '文件类型发生变化（例如，从普通文件变为符号链接）',
    },
    {
      id: 10,
      name: 'BROKEN',
      description: '文件状态未知或无效',
    },
    {
      id: 11,
      name: 'CONFLICTED',
      description: '文件处于冲突状态，通常发生在合并或变基操作中',
    },
    {
      id: 12,
      name: 'MODIFIED_AND_STAGED',
      description: '文件已被修改并已暂存',
    },
    {
      id: 13,
      name: 'DELETED_AND_STAGED',
      description: '文件已被删除并已暂存',
    },
    {
      id: 14,
      name: 'RENAMED_AND_STAGED',
      description: '文件已被重命名并已暂存',
    },
    {
      id: 15,
      name: 'COPIED_AND_STAGED',
      description: '文件已被复制并已暂存',
    },
  ];
  return statusList.find((item) => item.id === gitStatus)?.description;
}
