{"name": "@joycoder/plugin-base-ai", "version": "3.0.0", "description": "", "author": "JoyCoder", "homepage": "https://coding.jd.com/JoyCoder/JoyCoder-VSCode.git", "private": true, "license": "MIT", "main": "src/index.ts", "module": "src/index.ts", "files": ["src"], "repository": {"type": "git", "url": "https://coding.jd.com/JoyCoder/JoyCoder-VSCode.git"}, "scripts": {}, "bugs": {"url": "https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/"}, "publishConfig": {"access": "public"}, "dependencies": {"@langchain/community": "^0.0.32", "@langchain/core": "^0.1.32", "@langchain/openai": "^0.0.14", "@joycoder/plugin-custom-quick-jump": "workspace:*", "@joycoder/shared": "workspace:*", "@joycoder/web": "workspace:*", "@joycoder/agent-common": "workspace:*", "eventsource-parser": "^0.1.0", "node-fetch": "^3.3.0", "uuid": "^9.0.1", "sm-crypto": "^0.3.13"}}