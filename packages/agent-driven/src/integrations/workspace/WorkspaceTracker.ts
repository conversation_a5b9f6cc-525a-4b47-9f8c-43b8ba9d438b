import * as vscode from 'vscode';
import * as path from 'path';
import { listFiles, remoteGlobbyLevelByLevel } from '../../services/glob/list-files';
import { JoyCoderProvider } from '../../core/webview/JoycoderProvider';
import { getVscodeConfig } from '@joycoder/shared';

import { isRemoteEnvironment } from '../../utils/fs';
import { FileSystemHelper } from '../../utils/FileSystemHelper';
import { globby } from 'globby';

// const cwd = vscode.workspace.workspaceFolders?.map((folder) => folder.uri.fsPath).at(0);
const cwd = vscode.workspace.workspaceFolders
  ?.map((folder) => (isRemoteEnvironment() ? folder.uri : folder.uri.fsPath))
  .at(0);

// Note: this is not a drop-in replacement for listFiles at the start of tasks, since that will be done for Desktops when there is no workspace selected
class WorkspaceTracker {
  private providerRef: WeakRef<JoyCoderProvider>;
  private disposables: vscode.Disposable[] = [];
  private filePaths: Set<string> = new Set();
  private resourceFiles: string[] = [];

  constructor(provider: JoyCoderProvider) {
    this.providerRef = new WeakRef(provider);
    this.registerListeners();
  }

  /**
   * 异步填充文件路径列表。
   * 遍历工作目录，获取文件列表并添加到 filePaths 集合中。
   * 完成后触发工作区更新。
   * 注：桌面环境不自动获取，以避免权限弹窗。
   */
  async populateFilePaths(isActiveChange?: boolean) {
    // 如果没有工作空间，发送空的文件路径数组，确保前端能正确检测到没有工作空间
    if (!cwd) {
      const ruleFiles = await this.getRuleFiles();
      this.workspaceDidUpdate(ruleFiles);
      return;
    }
    this.workspaceUpdating();
    let filesWithSlashes: string[] = [];
    try {
      if (isActiveChange) {
        if (this.filePaths.size === 0) return;
        const filePaths = this.filePaths;
        filesWithSlashes = Array.from(filePaths);
        this.filePaths.clear();
      } else {
        let listFileNumber = getVscodeConfig('JoyCode.coder.selectFileNubmer');
        listFileNumber = !isNaN(Number(listFileNumber)) ? Number(listFileNumber) : 2_000;
        const [files, _] = await listFiles(cwd, true, listFileNumber || 2_000);
        filesWithSlashes = files;
      }
      const openedFiles = this.getOpenedFiles();
      const uniqueFiles = new Set([...openedFiles, ...filesWithSlashes]);
      filesWithSlashes = Array.from(uniqueFiles);
    } catch (error) {
      console.error('%c [ add-open-file-error ]-43', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
    filesWithSlashes.forEach((file) => this.filePaths.add(this.normalizeFilePath(file)));

    // 获取规则文件
    const ruleFiles = await this.getRuleFiles();
    this.workspaceDidUpdate(ruleFiles);
  }
  private getOpenedFiles() {
    if (!cwd) {
      return [];
    }
    const cwdStr = FileSystemHelper.getRemotePath(cwd);
    // 获取所有编辑器标签（需要 VS Code 1.67+）
    const tabGroups = vscode.window.tabGroups;
    const allTabs = tabGroups.all.flatMap((group) => group.tabs);
    const activeTab = allTabs.find(
      (tab) => tab.input instanceof vscode.TabInputText && tab.input.uri.fsPath.startsWith(cwdStr) && tab.isActive,
    );
    const visiblePath = activeTab && activeTab.input instanceof vscode.TabInputText ? activeTab.input.uri.fsPath : '';
    const openedFiles = allTabs
      .filter((tab) => tab.input instanceof vscode.TabInputText && tab.input.uri.fsPath.startsWith(cwdStr))
      .map((tab) => (tab.input as vscode.TabInputText).uri.fsPath)
      .sort((a, b) => {
        if (a === visiblePath) return -1;
        if (b === visiblePath) return 1;
        return 0;
      });
    return openedFiles;
  }
  private registerListeners() {
    // Listen for file creation
    // .bind(this) ensures the callback refers to class instance when using this, not necessary when using arrow function
    this.disposables.push(vscode.workspace.onDidCreateFiles(this.onFilesCreated.bind(this)));
    const watcher = vscode.workspace.createFileSystemWatcher('**/*.md');
    watcher.onDidCreate(async (uri) => {
      // 获取所有规则文件，等待操作完成
      const ruleFiles = await this.getRuleFiles();
      // 获取所有 AI 资源文件，并赋值给 this.resourceFiles
      this.resourceFiles = await this.getAiResourceFiles();
      // 工作区内容更新时，调用 workspaceDidUpdate 方法
      this.workspaceDidUpdate(ruleFiles);
    });
    watcher.onDidDelete(async (uri) => {
      // 获取所有规则文件，等待操作完成
      const ruleFiles = await this.getRuleFiles();
      // 获取所有 AI 资源文件，并赋值给 this.resourceFiles
      this.resourceFiles = await this.getAiResourceFiles();
      // 工作区内容更新时，调用 workspaceDidUpdate 方法
      this.workspaceDidUpdate(ruleFiles);
    });

    // Listen for file deletion
    this.disposables.push(vscode.workspace.onDidDeleteFiles(this.onFilesDeleted.bind(this)));

    // Listen for file renaming
    this.disposables.push(vscode.workspace.onDidRenameFiles(this.onFilesRenamed.bind(this)));

    // 监听活动编辑器的变化
    this.disposables.push(vscode.window.onDidChangeActiveTextEditor(this.onActiveTextEditorChange.bind(this)));

    /*
		 An event that is emitted when a workspace folder is added or removed.
		 **Note:** this event will not fire if the first workspace folder is added, removed or changed,
		 because in that case the currently executing extensions (including the one that listens to this
		 event) will be terminated and restarted so that the (deprecated) `rootPath` property is updated
		 to point to the first workspace folder.
		 */
    // In other words, we don't have to worry about the root workspace folder ([0]) changing since the extension will be restarted and our cwd will be updated to reflect the new workspace folder. (We don't care about non root workspace folders, since joycoder will only be working within the root folder cwd)
    // this.disposables.push(vscode.workspace.onDidChangeWorkspaceFolders(this.onWorkspaceFoldersChanged.bind(this)))
  }

  private async onActiveTextEditorChange() {
    // this.filePaths.clear();
    await this.populateFilePaths(true);
  }
  private async onFilesCreated(event: vscode.FileCreateEvent) {
    // 等待所有新创建文件的处理操作完成
    await Promise.all(
      event.files.map(async (file) => {
        // 对每个新创建的文件，调用 addFilePath 方法添加文件路径
        await this.addFilePath(file.fsPath);
      }),
    );
    // 获取所有规则文件，等待操作完成
    const ruleFiles = await this.getRuleFiles();
    // 获取所有 AI 资源文件，并赋值给 this.resourceFiles
    this.resourceFiles = await this.getAiResourceFiles();
    // 工作区内容更新时，调用 workspaceDidUpdate 方法
    this.workspaceDidUpdate(ruleFiles);
  }

  private async onFilesDeleted(event: vscode.FileDeleteEvent) {
    let updated = false;
    await Promise.all(
      event.files.map(async (file) => {
        if (await this.removeFilePath(file.fsPath)) {
          updated = true;
        }
      }),
    );
    if (updated) {
      const ruleFiles = await this.getRuleFiles();
      this.resourceFiles = await this.getAiResourceFiles();

      this.workspaceDidUpdate(ruleFiles);
    }
  }

  private async onFilesRenamed(event: vscode.FileRenameEvent) {
    await Promise.all(
      event.files.map(async (file) => {
        await this.removeFilePath(file.oldUri.fsPath);
        await this.addFilePath(file.newUri.fsPath);
      }),
    );
    const ruleFiles = await this.getRuleFiles();
    this.resourceFiles = await this.getAiResourceFiles();

    this.workspaceDidUpdate(ruleFiles);
  }
  private workspaceUpdating() {
    if (!cwd) {
      return;
    }
    this.providerRef.deref()?.postMessageToWebview({
      type: 'workspaceUpdating',
      updateStatus: 'Updating',
    });
  }
  /**
   * 获取 .joycode/ai-resource  目录下的 *.md 文件列表
   * 如果目录不存在，则返回空数组
   */
  private async getAiResourceFiles(): Promise<string[]> {
    if (!cwd) {
      console.log('No valid workspace folder is opened');
      return [];
    }

    try {
      const aiResourceDir = vscode.Uri.joinPath(FileSystemHelper.getUri(cwd), '.joycode', 'ai-resource');

      // 检查目录是否存在
      try {
        await vscode.workspace.fs.stat(aiResourceDir);
      } catch (error) {
        // 目录不存在，返回空数组
        console.log('AI resource directory does not exist:', aiResourceDir.fsPath);
        return [];
      }
      // 获取 *.md 文件
      if (isRemoteEnvironment()) {
        const mdFiles = await remoteGlobbyLevelByLevel(100, {
          cwd: aiResourceDir.toString(),
          dot: true,
          absolute: true,
          markDirectories: true,
          gitignore: false,
          ignore: undefined,
          onlyFiles: false,
          suppressErrors: true,
        });

        return mdFiles.filter((file) => file.endsWith('.md'));
      } else {
        const mdFiles = await globby(['*.md'], {
          cwd: aiResourceDir.fsPath,
          absolute: true,
        });
        return mdFiles;
      }
    } catch (error) {
      console.error('获取AI资源文件失败:', error);
      return [];
    }
  }

  /**
   * 获取 .joycode/rules 目录下的 *.mdc 文件列表
   * 如果目录不存在，则返回空数组
   */
  private async getRuleFiles(): Promise<string[]> {
    if (!cwd) {
      console.log('No valid workspace folder is opened');
      return [];
    }

    try {
      const cwdUri = FileSystemHelper.getUri(cwd);
      const rulesDirJoycode = vscode.Uri.joinPath(cwdUri, '.joycode');
      const rulesDirJoycoder = vscode.Uri.joinPath(cwdUri, '.joycoder');

      try {
        // 检查.joycode目录是否存在
        await vscode.workspace.fs.stat(rulesDirJoycode);
      } catch (error) {
        try {
          // 检查.joycoder目录是否存在
          await vscode.workspace.fs.stat(rulesDirJoycoder);

          try {
            // 先检查配置文件是否存在
            const JoycoderMcpPath = vscode.Uri.joinPath(rulesDirJoycoder, 'joycoder-mcp.json');
            await vscode.workspace.fs.stat(JoycoderMcpPath);

            // 如果配置文件存在,先重命名文件夹,这样新的配置文件路径就会基于新文件夹
            await vscode.workspace.fs.rename(rulesDirJoycoder, rulesDirJoycode, { overwrite: true });

            // 重命名配置文件
            const JoycodeMcpPath = vscode.Uri.joinPath(rulesDirJoycode, 'joycode-mcp.json');
            const newJoycoderMcpPath = vscode.Uri.joinPath(rulesDirJoycode, 'joycoder-mcp.json');
            await vscode.workspace.fs.rename(newJoycoderMcpPath, JoycodeMcpPath, { overwrite: true });
          } catch (error) {
            // 如果配置文件不存在,直接重命名文件夹
            await vscode.workspace.fs.rename(rulesDirJoycoder, rulesDirJoycode, { overwrite: true });
          }
        } catch (error) {
          // .joycoder目录不存在,创建.joycode目录
          await vscode.workspace.fs.createDirectory(rulesDirJoycode);
          console.log('Created rules directory:', rulesDirJoycode.fsPath);
        }
      }

      const rulesDir = vscode.Uri.joinPath(FileSystemHelper.getUri(cwd), '.joycode', 'rules');
      // 检查目录是否存在，如果不存在则创建
      try {
        await vscode.workspace.fs.stat(rulesDir);
      } catch (error) {
        // 目录不存在，创建目录
        await vscode.workspace.fs.createDirectory(rulesDir);
        console.log('Created rules directory:', rulesDir.fsPath);
      }

      // 获取 *.mdc、*.md 文件
      if (isRemoteEnvironment()) {
        const mdcFiles = await remoteGlobbyLevelByLevel(100, {
          cwd: rulesDir.toString(),
          dot: true, // do not ignore hidden files/directories
          absolute: true,
          markDirectories: true, // Append a / on any directories matched (/ is used on windows as well, so dont use path.sep)
          gitignore: false, // globby ignores any files that are gitignored
          ignore: undefined, // just in case there is no gitignore, we ignore sensible defaults
          onlyFiles: false, // true by default, false means it will list directories on their own too
          suppressErrors: true,
        });
        return mdcFiles;
      } else {
        const mdcFiles = await globby(['*.mdc', '*.md'], {
          cwd: rulesDir!.fsPath,
          absolute: true,
        });
        return mdcFiles;
      }
    } catch (error) {
      console.error('获取规则文件失败:', error);
      return [];
    }
  }

  private async workspaceDidUpdate(ruleFiles: string[]) {
    // 如果没有工作空间，发送空的文件路径数组，确保前端能正确检测到没有工作空间
    if (!cwd) {
      this.providerRef.deref()?.postMessageToWebview({
        type: 'workspaceUpdated',
        filePaths: [], // 空数组表示没有工作空间
        ruleFiles: [],
        updateStatus: 'done',
      });
      return;
    }
    this.resourceFiles = await this.getAiResourceFiles();

    this.providerRef.deref()?.postMessageToWebview({
      type: 'workspaceUpdated',
      filePaths: Array.from(this.filePaths).map((file) => {
        const relativePath = path.relative(FileSystemHelper.getRemotePath(cwd), file).toPosix();
        return file.endsWith('/') ? relativePath + '/' : relativePath;
      }),
      ruleFiles: ruleFiles.map((file) => {
        const relativePath = FileSystemHelper.relative(FileSystemHelper.getRemotePath(cwd), file).toPosix();
        return relativePath;
      }),
      resourceFiles: this.resourceFiles.map((file) => {
        const relativePath = FileSystemHelper.relative(FileSystemHelper.getRemotePath(cwd), file).toPosix();
        return '/' + relativePath;
      }),
      updateStatus: 'done',
    });
  }

  private normalizeFilePath(filePath: string): string {
    const resolvedPath = cwd ? path.resolve(FileSystemHelper.getRemotePath(cwd), filePath) : path.resolve(filePath);
    return filePath.endsWith('/') ? resolvedPath + '/' : resolvedPath;
  }

  private async addFilePath(filePath: string): Promise<string> {
    const normalizedPath = this.normalizeFilePath(filePath);
    try {
      const stat = await vscode.workspace.fs.stat(vscode.Uri.file(normalizedPath));
      const isDirectory = (stat.type & vscode.FileType.Directory) !== 0;
      const pathWithSlash = isDirectory && !normalizedPath.endsWith('/') ? normalizedPath + '/' : normalizedPath;
      this.filePaths.add(pathWithSlash);
      return pathWithSlash;
    } catch {
      // If stat fails, assume it's a file (this can happen for newly created files)
      this.filePaths.add(normalizedPath);
      return normalizedPath;
    }
  }

  private async removeFilePath(filePath: string): Promise<boolean> {
    const normalizedPath = this.normalizeFilePath(filePath);
    return this.filePaths.delete(normalizedPath) || this.filePaths.delete(normalizedPath + '/');
  }

  public dispose() {
    this.disposables.forEach((d) => d.dispose());
  }
}

export default WorkspaceTracker;
