import * as vscode from 'vscode';
import { ExperimentId } from '../schemas';
import { addCustomInstructions } from '../core/prompts/sections';
import { ALWAYS_AVAILABLE_TOOLS } from '../core/prompts/tools/index';
import {
  modes,
  getAllModes,
  PromptComponent,
  getModeBySlug,
  ToolGroup,
  TOOL_GROUPS,
  CustomModePrompts,
  GroupEntry,
  GroupOptions,
  ModeConfig,
} from '../../web-agent/src/utils/modes';

export type { ExperimentId };

export const EXPERIMENT_IDS = {
  POWER_STEERING: 'powerSteering',
} as const satisfies Record<string, ExperimentId>;

export type Mode = string;

export type { GroupOptions, GroupEntry, ModeConfig, PromptComponent, CustomModePrompts };

// Helper to extract group name regardless of format
export function getGroupName(group: GroupEntry): ToolGroup {
  if (typeof group === 'string') {
    return group;
  }

  return group[0];
}

// Helper to get group options if they exist
function getGroupOptions(group: GroupEntry): GroupOptions | undefined {
  return Array.isArray(group) ? group[1] : undefined;
}

// Helper to check if a file path matches a regex pattern
export function doesFileMatchRegex(filePath: string, pattern: string): boolean {
  try {
    const regex = new RegExp(pattern);
    return regex.test(filePath);
  } catch (error) {
    console.error(`Invalid regex pattern: ${pattern}`, error);
    return false;
  }
}

// Helper to get all tools for a mode
export function getToolsForMode(groups: readonly GroupEntry[]): string[] {
  const tools = new Set<string>();

  // Add tools from each group
  groups.forEach((group) => {
    const groupName = getGroupName(group);
    const groupConfig = TOOL_GROUPS[groupName];
    groupConfig.tools.forEach((tool: string) => tools.add(tool));
  });

  // Always add required tools
  ALWAYS_AVAILABLE_TOOLS.forEach((tool: string) => tools.add(tool));

  return Array.from(tools);
}

// Check if a mode is custom or an override
export function isCustomMode(agentId: string, customModes?: ModeConfig[]): boolean {
  return !!customModes?.some((mode) => mode.agentId === agentId);
}

// Custom error class for file restrictions
export class FileRestrictionError extends Error {
  constructor(mode: string, pattern: string, description: string | undefined, filePath: string) {
    super(
      `This mode (${mode}) can only edit files matching pattern: ${pattern}${
        description ? ` (${description})` : ''
      }. Got: ${filePath}`
    );
    this.name = 'FileRestrictionError';
  }
}

export function isToolAllowedForMode(
  tool: string,
  modeSlug: string,
  customModes: ModeConfig[],
  toolRequirements?: Record<string, boolean>,
  toolParams?: Record<string, any>, // All tool parameters
  experiments?: Record<string, boolean>
): boolean {
  // Always allow these tools
  if (ALWAYS_AVAILABLE_TOOLS.includes(tool as any)) {
    return true;
  }
  if (experiments && Object.values(EXPERIMENT_IDS).includes(tool as ExperimentId)) {
    if (!experiments[tool]) {
      return false;
    }
  }

  // Check tool requirements if any exist
  if (toolRequirements && typeof toolRequirements === 'object') {
    if (tool in toolRequirements && !toolRequirements[tool]) {
      return false;
    }
  } else if (toolRequirements === false) {
    // If toolRequirements is a boolean false, all tools are disabled
    return false;
  }

  const mode = getModeBySlug(modeSlug, customModes);
  if (!mode) {
    return false;
  }

  // Check if tool is in any of the mode's groups and respects any group options
  for (const group of mode.groups) {
    const groupName = getGroupName(group);
    const options = getGroupOptions(group);

    const groupConfig = TOOL_GROUPS[groupName];

    // If the tool isn't in this group's tools, continue to next group
    if (!groupConfig.tools.includes(tool)) {
      continue;
    }

    // If there are no options, allow the tool
    if (!options) {
      return true;
    }

    // For the edit group, check file regex if specified
    if (groupName === 'edit' && options.fileRegex) {
      const filePath = toolParams?.path;
      if (
        filePath &&
        (toolParams.diff || toolParams.content || toolParams.operations) &&
        !doesFileMatchRegex(filePath, options.fileRegex)
      ) {
        throw new FileRestrictionError(mode.name, options.fileRegex, options.description, filePath);
      }
    }

    return true;
  }

  return false;
}

// Helper function to get all modes with their prompt overrides from extension state
export async function getAllModesWithPrompts(context: vscode.ExtensionContext): Promise<ModeConfig[]> {
  const customModes = (await context.globalState.get<ModeConfig[]>('customModes')) || [];
  const customModePrompts = (await context.globalState.get<CustomModePrompts>('customModePrompts')) || {};

  const allModes = getAllModes(customModes);
  return allModes.map((mode) => ({
    ...mode,
    agentDefinition: customModePrompts[mode.agentId]?.agentDefinition ?? mode.agentDefinition,
    customInstructions: customModePrompts[mode.agentId]?.customInstructions ?? mode.customInstructions,
  }));
}

// Helper function to get complete mode details with all overrides
export async function getFullModeDetails(
  modeSlug: string,
  customModes?: ModeConfig[],
  customModePrompts?: CustomModePrompts,
  options?: {
    cwd?: string;
    globalCustomInstructions?: string;
    language?: string;
  }
): Promise<ModeConfig> {
  // First get the base mode config from custom modes or built-in modes
  const baseMode = getModeBySlug(modeSlug, customModes) || modes.find((m) => m.agentId === modeSlug) || modes[0];

  // Check for any prompt component overrides
  const promptComponent = customModePrompts?.[modeSlug];

  // Get the base custom instructions
  const baseCustomInstructions = promptComponent?.customInstructions || baseMode.customInstructions || '';

  // If we have cwd, load and combine all custom instructions
  let fullCustomInstructions = baseCustomInstructions;
  if (options?.cwd) {
    fullCustomInstructions = await addCustomInstructions(
      baseCustomInstructions,
      options.globalCustomInstructions || '',
      options.cwd,
      modeSlug,
      { language: options.language }
    );
  }

  // Return mode with any overrides applied
  return {
    ...baseMode,
    agentDefinition: promptComponent?.agentDefinition || baseMode.agentDefinition,
    customInstructions: fullCustomInstructions,
  };
}
