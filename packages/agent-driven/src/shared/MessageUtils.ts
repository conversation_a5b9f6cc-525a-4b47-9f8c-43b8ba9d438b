import { JoyCoderMessage } from './ExtensionMessage';

/**
 * 消息工具类 - 提供统一的消息处理逻辑
 */
export class MessageUtils {
  /**
   * 判断消息是否为用户消息
   * 优先使用Node层设置的isUserMessage标识，如果未设置则使用say字段判断
   */
  static isUserMessage(message: JoyCoderMessage): boolean {
    if (message.isUserMessage !== undefined) {
      return message.isUserMessage;
    }
    return (
      message.say === 'user_feedback' ||
      message.say === 'user_feedback_diff' ||
      // 特殊情况：第一条消息可能是say: "text"但实际是用户输入
      (message.say === 'text' && message.conversationHistoryIndex === -1)
    );
  }

  /**
   * 判断消息是否为checkpoint消息
   */
  static isCheckpointMessage(message: JoyCoderMessage): boolean {
    return message.say === 'checkpoint_created';
  }

  /**
   * 判断消息是否为AI消息（非用户消息且非checkpoint消息）
   */
  static isAIMessage(message: JoyCoderMessage): boolean {
    return !this.isUserMessage(message) && !this.isCheckpointMessage(message);
  }

  /**
   * 判断消息是否正在生成中
   */
  static isMessageGenerating(
    message: JoyCoderMessage,
    isLast: boolean,
    lastModifiedMessage?: JoyCoderMessage
  ): boolean {
    if (message.partial === true) {
      return true;
    }
    if (isLast && lastModifiedMessage) {
      if (lastModifiedMessage.ts === message.ts && lastModifiedMessage.partial === true) {
        return true;
      }
    }
    return false;
  }

  /**
   * 获取消息的文本内容
   * 对于某些消息类型需要解析JSON格式的text字段
   */
  static getMessageText(message: JoyCoderMessage): string {
    if (message.say === 'text' || message.say === 'completion_result') {
      try {
        const parsed = JSON.parse(message.text || '{}');
        return parsed.text || message.text || '';
      } catch {
        return message.text || '';
      }
    }
    return message.text || '';
  }

  static getNextMessage(
    allMessages?: (JoyCoderMessage | JoyCoderMessage[])[],
    messageIndex?: number
  ): JoyCoderMessage | null {
    if (!allMessages || messageIndex === undefined) return null;
    const nextMessageIndex = messageIndex + 1;
    if (nextMessageIndex >= allMessages.length) return null;
    const nextMessage = allMessages[nextMessageIndex];
    return Array.isArray(nextMessage) ? nextMessage[0] : nextMessage;
  }

  static getPreviousMessage(
    allMessages?: (JoyCoderMessage | JoyCoderMessage[])[],
    messageIndex?: number
  ): JoyCoderMessage | null {
    if (!allMessages || messageIndex === undefined) return null;
    const prevMessageIndex = messageIndex - 1;
    if (prevMessageIndex < 0) return null;
    const prevMessage = allMessages[prevMessageIndex];
    return Array.isArray(prevMessage) ? prevMessage[prevMessage.length - 1] : prevMessage;
  }

  /**
   * 判断是否应该显示头像
   * 规则：
   * 1. 用户消息不显示头像
   * 2. AI消息在以下情况显示头像：
   *    - 用户消息后的第一条非checkpoint消息
   *    - 智能体切换后的第一条消息
   */
  static shouldShowAvatar(
    currentMessage: JoyCoderMessage,
    allMessages?: (JoyCoderMessage | JoyCoderMessage[])[],
    messageIndex?: number
  ): boolean {
    if (this.isUserMessage(currentMessage)) {
      return false;
    }
    if (this.isCheckpointMessage(currentMessage)) {
      return false;
    }
    if (!allMessages || messageIndex === undefined) {
      return false;
    }

    // 检查是否是智能体切换后的第一条消息
    if (this.isAIMessage(currentMessage) && currentMessage.modeInfo) {
      // 向前查找最近的非checkpoint消息，检查智能体是否不同
      for (let i = messageIndex - 1; i >= 0; i--) {
        const prevMsg = allMessages[i];
        const actualPrevMsg = Array.isArray(prevMsg) ? prevMsg[prevMsg.length - 1] : prevMsg;
        if (!actualPrevMsg) continue;

        // 跳过checkpoint消息
        if (this.isCheckpointMessage(actualPrevMsg)) {
          continue;
        }

        // 如果遇到AI消息，检查智能体是否不同
        if (this.isAIMessage(actualPrevMsg) && actualPrevMsg.modeInfo) {
          if (actualPrevMsg.modeInfo.agentId !== currentMessage.modeInfo.agentId) {
            // 智能体不同，显示头像
            return true;
          } else {
            // 智能体相同，不显示头像
            return false;
          }
        }

        // 如果遇到用户消息，说明这是用户消息后的第一条AI消息
        // 但我们仍然需要检查是否是智能体切换
        if (this.isUserMessage(actualPrevMsg)) {
          // 继续向前查找，看看用户消息之前是否有不同智能体的AI消息
          for (let j = i - 1; j >= 0; j--) {
            const prevPrevMsg = allMessages[j];
            const actualPrevPrevMsg = Array.isArray(prevPrevMsg) ? prevPrevMsg[prevPrevMsg.length - 1] : prevPrevMsg;
            if (!actualPrevPrevMsg) continue;

            // 跳过checkpoint消息
            if (this.isCheckpointMessage(actualPrevPrevMsg)) {
              continue;
            }

            // 如果找到AI消息，检查智能体是否不同
            if (this.isAIMessage(actualPrevPrevMsg) && actualPrevPrevMsg.modeInfo) {
              if (actualPrevPrevMsg.modeInfo.agentId !== currentMessage.modeInfo.agentId) {
                // 智能体不同，显示头像
                return true;
              }
              // 找到了相同智能体的AI消息，跳出内层循环
              break;
            }

            // 如果遇到另一个用户消息，停止查找
            if (this.isUserMessage(actualPrevPrevMsg)) {
              break;
            }
          }
          // 跳出外层循环，进入原有逻辑
          break;
        }
      }
    }

    // 原有逻辑：向前查找最近的用户消息
    for (let i = messageIndex - 1; i >= 0; i--) {
      const prevMsg = allMessages[i];
      const actualPrevMsg = Array.isArray(prevMsg) ? prevMsg[prevMsg.length - 1] : prevMsg;
      if (!actualPrevMsg) continue;
      if (this.isUserMessage(actualPrevMsg)) {
        // 检查从用户消息到当前消息之间是否都是checkpoint消息
        for (let j = i + 1; j < messageIndex; j++) {
          const betweenMsg = allMessages[j];
          const actualBetweenMsg = Array.isArray(betweenMsg) ? betweenMsg[betweenMsg.length - 1] : betweenMsg;
          if (actualBetweenMsg && !this.isCheckpointMessage(actualBetweenMsg)) {
            return false;
          }
        }
        return true;
      }
      if (!this.isCheckpointMessage(actualPrevMsg)) {
        return false;
      }
    }
    return false;
  }

  /**
   * 判断是否应该显示工具条
   * 只有checkpoint_created消息显示工具条
   */
  static shouldShowToolbar(message: JoyCoderMessage): boolean {
    return message.say === 'checkpoint_created';
  }

  /**
   * 判断是否应该显示回滚按钮
   * 远程环境下不显示回滚按钮
   */
  static shouldShowCheckmarkControl(
    currentMessage?: JoyCoderMessage,
    messageTs?: number,
    isRemoteEnvironment?: boolean
  ): boolean {
    if (isRemoteEnvironment) {
      return false;
    }
    if (currentMessage?.say === 'checkpoint_created' && messageTs != null) {
      return true;
    }
    return false;
  }

  static getCheckmarkMessageTs(currentMessage?: JoyCoderMessage, messageTs?: number): number | undefined {
    if (currentMessage?.say === 'checkpoint_created' && messageTs != null) {
      return messageTs;
    }
    return undefined;
  }

  static getCheckmarkPreviousMessage(
    currentMessage?: JoyCoderMessage,
    previousMessage?: JoyCoderMessage | null
  ): JoyCoderMessage | null {
    if (currentMessage?.say === 'checkpoint_created') {
      return previousMessage || null;
    }
    return null;
  }

  /**
   * 判断工具条按钮是否应该靠左对齐
   * 当checkpoint消息的上一条消息是AI消息时，靠左对齐
   */
  static shouldAlignToolbarLeft(currentMessage?: JoyCoderMessage, previousMessage?: JoyCoderMessage | null): boolean {
    if (!currentMessage) return false;
    if (currentMessage.say === 'checkpoint_created' && previousMessage) {
      return this.isAIMessage(previousMessage);
    }
    return false;
  }

  /**
   * 检查工具条数据是否准备完成，避免闪烁
   */
  static isToolbarDataReady(
    currentMessage?: JoyCoderMessage,
    showCheckmarkControl?: boolean,
    allMessages?: (JoyCoderMessage | JoyCoderMessage[])[],
    messageIndex?: number
  ): boolean {
    if (!currentMessage) return false;
    if (showCheckmarkControl && currentMessage.say === 'checkpoint_created') {
      return !!(allMessages && messageIndex !== undefined);
    }
    return true;
  }

  /**
   * 判断是否应该显示检查点覆盖层
   * 注意：对于被中断且未响应的工具，不会有检查点哈希
   */
  static shouldShowCheckpoints(
    message: JoyCoderMessage,
    isLast: boolean,
    lastModifiedMessage?: JoyCoderMessage
  ): boolean {
    let shouldShow =
      message.lastCheckpointHash != null &&
      (message.say === 'tool' ||
        message.ask === 'tool' ||
        message.say === 'command' ||
        message.ask === 'command' ||
        message.say === 'use_mcp_server' ||
        message.ask === 'use_mcp_server');

    if (shouldShow && isLast) {
      shouldShow = lastModifiedMessage?.ask === 'resume_completed_task' || lastModifiedMessage?.ask === 'resume_task';
    }
    return shouldShow;
  }

  /**
   * 检查是否存在reasoning消息
   */
  static hasReasoningMessage(allMessages?: (JoyCoderMessage | JoyCoderMessage[])[], messageIndex?: number): boolean {
    if (!allMessages || messageIndex === undefined) return false;
    for (let i = messageIndex + 1; i < allMessages.length; i++) {
      const nextMsg = allMessages[i];
      const actualNextMsg = Array.isArray(nextMsg) ? nextMsg[0] : nextMsg;
      if (actualNextMsg && actualNextMsg.say === 'reasoning') {
        return true;
      }
    }
    return false;
  }

  /**
   * 判断是否应该显示复制按钮
   * 在checkpoint消息中，如果上一条消息是用户消息，则显示复制按钮
   */
  static shouldShowCopyButton(currentMessage?: JoyCoderMessage, previousMessage?: JoyCoderMessage | null): boolean {
    if (!currentMessage) return false;
    if (currentMessage.say === 'checkpoint_created' && previousMessage) {
      return this.isUserMessage(previousMessage);
    }
    return false;
  }

  /**
   * 获取用户消息的文本内容
   * 如果当前消息是checkpoint且上一条消息是用户消息，则返回上一条消息的文本
   */
  static getUserMessageText(currentMessage: JoyCoderMessage, previousMessage?: JoyCoderMessage | null): string {
    if (currentMessage.say === 'checkpoint_created' && previousMessage && this.isUserMessage(previousMessage)) {
      return this.getMessageText(previousMessage);
    }
    return '';
  }
}
