import fs from 'fs';
import path from 'path';

interface ImageValidationOptions {
  maxSize?: number; // 最大文件大小(bytes)
  allowedTypes?: string[]; // 允许的图片类型
}

type ImageSignatures = {
  [key: string]: number[][];
};

type ImageMimeTypes = {
  [key: string]: string;
};

export class ImageUtils {
  // 支持的图片类型及其对应的魔数(Magic Numbers)
  private static readonly IMAGE_SIGNATURES: ImageSignatures = {
    jpg: [
      [0xff, 0xd8, 0xff, 0xe0],
      [0xff, 0xd8, 0xff, 0xe1],
      [0xff, 0xd8, 0xff, 0xe8],
    ],
    jpeg: [
      [0xff, 0xd8, 0xff, 0xe0],
      [0xff, 0xd8, 0xff, 0xe1],
      [0xff, 0xd8, 0xff, 0xe8],
    ],
    png: [[0x89, 0x50, 0x4e, 0x47]],
    gif: [[0x47, 0x49, 0x46, 0x38]],
    webp: [[0x52, 0x49, 0x46, 0x46]],
    bmp: [[0x42, 0x4d]],
    tiff: [
      [0x49, 0x49, 0x2a, 0x00], // Little-endian
      [0x4d, 0x4d, 0x00, 0x2a], // Big-endian
    ],
    ico: [[0x00, 0x00, 0x01, 0x00]],
    heic: [[0x66, 0x74, 0x79, 0x70, 0x68, 0x65, 0x69, 0x63]], // ftyp heic
    avif: [[0x66, 0x74, 0x79, 0x70, 0x61, 0x76, 0x69, 0x66]], // ftyp avif
  };

  // 默认支持的图片扩展名
  private static readonly DEFAULT_IMAGE_EXTENSIONS = [
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.bmp',
    '.webp',
    '.tiff',
    '.ico',
    '.heic',
    '.avif',
  ];

  // MIME类型映射
  private static readonly MIME_TYPES: ImageMimeTypes = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.bmp': 'image/bmp',
    '.webp': 'image/webp',
    '.tiff': 'image/tiff',
    '.ico': 'image/x-icon',
    '.heic': 'image/heic',
    '.avif': 'image/avif',
  };

  /**
   * 检查文件是否为有效的图片
   * @param filePath 文件路径
   * @param options 验证选项
   * @returns Promise<boolean>
   */
  public static async isValidImage(filePath: string, options: ImageValidationOptions = {}): Promise<boolean> {
    try {
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        return false;
      }

      // 获取文件扩展名
      const ext = path.extname(filePath).toLowerCase();
      const allowedTypes = options.allowedTypes || this.DEFAULT_IMAGE_EXTENSIONS;

      // 检查扩展名是否在允许列表中
      if (!allowedTypes.includes(ext)) {
        return false;
      }

      // 检查文件大小
      if (options.maxSize) {
        const stats = fs.statSync(filePath);
        if (stats.size > options.maxSize) {
          return false;
        }
      }

      // 读取文件头进行魔数检查
      const buffer = await fs.promises.readFile(filePath, { flag: 'r' });
      return this.verifyImageSignature(buffer, ext.substring(1));
    } catch (error) {
      console.error('Error validating image:', error);
      return false;
    }
  }

  /**
   * 同步检查文件是否为有效的图片
   * @param filePath 文件路径
   * @param options 验证选项
   * @returns boolean
   */
  public static isValidImageSync(filePath: string, options: ImageValidationOptions = {}): boolean {
    try {
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        return false;
      }

      // 获取文件扩展名
      const ext = path.extname(filePath).toLowerCase();
      const allowedTypes = options.allowedTypes || this.DEFAULT_IMAGE_EXTENSIONS;

      // 检查扩展名是否在允许列表中
      if (!allowedTypes.includes(ext)) {
        return false;
      }

      // 检查文件大小
      if (options.maxSize) {
        const stats = fs.statSync(filePath);
        if (stats.size > options.maxSize) {
          return false;
        }
      }

      // 读取文件头进行魔数检查
      const buffer = fs.readFileSync(filePath, { flag: 'r' });
      return this.verifyImageSignature(buffer, ext.substring(1));
    } catch (error) {
      console.error('Error validating image:', error);
      return false;
    }
  }

  /**
   * 获取文件的MIME类型
   * @param filePath 文件路径
   * @returns string | null
   */
  public static getImageMimeType(filePath: string): string | null {
    const ext = path.extname(filePath).toLowerCase();
    return this.MIME_TYPES[ext] || null;
  }

  /**
   * 验证文件头魔数
   * @param buffer 文件buffer
   * @param type 图片类型
   * @returns boolean
   */
  private static verifyImageSignature(buffer: Buffer, type: string): boolean {
    const signatures = this.IMAGE_SIGNATURES[type];
    if (!signatures) {
      return false;
    }

    return signatures.some((signature: number[]) => {
      return signature.every((byte: number, index: number) => buffer[index] === byte);
    });
  }

  /**
   * 获取支持的图片扩展名列表
   * @returns string[]
   */
  public static getSupportedExtensions(): string[] {
    return [...this.DEFAULT_IMAGE_EXTENSIONS];
  }

  /**
   * 检查文件大小是否在限制范围内
   * @param filePath 文件路径
   * @param maxSize 最大文件大小(bytes)
   * @returns boolean
   */
  public static checkImageSize(filePath: string, maxSize: number): boolean {
    try {
      const stats = fs.statSync(filePath);
      return stats.size <= maxSize;
    } catch (error) {
      console.error('Error checking image size:', error);
      return false;
    }
  }
}
