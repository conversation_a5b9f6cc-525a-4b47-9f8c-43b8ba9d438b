import { BrowserSettings } from '../../shared/BrowserSettings';
import { ToolArgs } from './tools/types';

/**
 * 获取所有工具描述
 */
export function getUseBrowserTool(args: ToolArgs): string {
  const toolDescriptions = [];
  const browserSettings = args.browserSettings as BrowserSettings;
  // 如果支持浏览器功能，添加浏览器相关工具
  if (args.supportsComputerUse && browserSettings) {
    toolDescriptions.push(`
## use_browser
Description: Request to interact with a Playwright-controlled browser. Playwright provides cross-browser automation with enhanced capabilities including automatic waiting, network interception, and multiple browser support (Chromium, Firefox, WebKit). Every action, except \`close\`, will be responded to with a screenshot of the browser's current state, along with any new console logs. You may only perform one browser action per message, and wait for the user's response including a screenshot and logs to determine the next action.
- The sequence of actions **must always start with** launching the browser at a URL, and **must always end with** closing the browser. If you need to visit a new URL that is not possible to navigate to from the current webpage, you must first close the browser, then launch again at the new URL.
- While the browser is active, only the \`use_browser\` tool can be used. No other tools should be called during this time. You may proceed to use other tools only after closing the browser. For example if you run into an error and need to fix a file, you must close the browser, then use other tools to make the necessary changes, then re-launch the browser to verify the result.
- The browser window has a resolution of **${browserSettings?.viewport?.width}x${browserSettings.viewport.height}** pixels. When performing any click actions, ensure the coordinates are within this resolution range.
- Playwright automatically waits for elements to be ready before interacting with them, reducing timing issues. It will wait for elements to be visible, enabled, and stable before performing actions.
- Before clicking on any elements such as icons, links, or buttons, you must consult the provided screenshot of the page to determine the coordinates of the element. The click should be targeted at the **center of the element**, not on its edges.
Parameters:
- action: (required) The action to perform. The available actions are:
    * launch: Launch a new Playwright-controlled browser instance at the specified URL. This **must always be the first action**.
        - Use with the \`url\` parameter to provide the URL.
        - Ensure the URL is valid and includes the appropriate protocol (e.g. http://localhost:3000/page, file:///path/to/file.html, etc.)
        - Playwright will automatically wait for the page to reach a stable state (load event fired, no network activity for 500ms).
    * hover: Move the cursor to a specific x,y coordinate.
        - Use with the \`coordinate\` parameter to specify the location.
        - Always move to the center of an element (icon, button, link, etc.) based on coordinates derived from a screenshot.
        - Playwright will ensure the element is visible and stable before hovering.
    * click: Click at a specific x,y coordinate.
        - Use with the \`coordinate\` parameter to specify the location.
        - Always click in the center of an element (icon, button, link, etc.) based on coordinates derived from a screenshot.
        - Playwright will automatically wait for the element to be clickable (visible, enabled, and stable).
    * type: Type a string of text on the keyboard. You might use this after clicking on a text field to input text.
        - Use with the \`text\` parameter to provide the string to type.
        - Playwright will ensure the element is focused and ready for input before typing.
    * resize: Resize the viewport to a specific w,h size.
        - Use with the \`size\` parameter to specify the new size.
    * scroll_down: Scroll down the page by one page height.
    * scroll_up: Scroll up the page by one page height.
    * close: Close the Playwright-controlled browser instance. This **must always be the final browser action**.
        - Example: \`<action>close</action>\`
- url: (optional) Use this for providing the URL for the \`launch\` action.
    * Example: <url>https://example.com</url>
- coordinate: (optional) The X and Y coordinates for the \`click\` and \`hover\` actions. Coordinates should be within the **${browserSettings?.viewport?.width}x${browserSettings.viewport.height}** resolution.
    * Example: <coordinate>450,300</coordinate>
- size: (optional) The width and height for the \`resize\` action.
    * Example: <size>1280,720</size>
- text: (optional) Use this for providing the text for the \`type\` action.
    * Example: <text>Hello, world!</text>
Usage:
<use_browser>
<action>Action to perform (e.g., launch, click, type, scroll_down, scroll_up, wait, close)</action>
<url>URL to launch the browser at (optional)</url>
<coordinate>x,y coordinates (optional)</coordinate>
<text>Text to type (optional)</text>
</use_browser>

Example: Requesting to launch a browser at https://example.com
<use_browser>
<action>launch</action>
<url>https://example.com</url>
</use_browser>

Example: Requesting to click on the element at coordinates 450,300
<use_browser>
<action>click</action>
<coordinate>450,300</coordinate>
</use_browser>
`);

    //     toolDescriptions.push(`
    // ## browser_goto
    // Description: Navigate the browser to a specific URL.
    // Parameters:
    // - url: (required) The URL to navigate to.
    // Usage:
    // <browser_goto>
    // <url>https://example.com</url>
    // </browser_goto>
    // `);

    //     toolDescriptions.push(`
    // ## browser_click
    // Description: Click on an element in the browser.
    // Parameters:
    // - selector: (required) The CSS selector of the element to click.
    // Usage:
    // <browser_click>
    // <selector>#submit-button</selector>
    // </browser_click>
    // `);

    //     toolDescriptions.push(`
    // ## browser_fill
    // Description: Fill a form field in the browser.
    // Parameters:
    // - selector: (required) The CSS selector of the form field.
    // - value: (required) The value to fill in.
    // Usage:
    // <browser_fill>
    // <selector>#username</selector>
    // <value>johndoe</value>
    // </browser_fill>
    // `);
  }

  return toolDescriptions.join('\n\n');
}
