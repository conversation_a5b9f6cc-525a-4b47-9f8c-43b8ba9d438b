import { ToolArgs } from './types';

export function getUseWriteFileDescription(args: ToolArgs): string {
  return `
## use_write_file
Description: Request to write content to a file. This tool is primarily used for **creating new files** or for scenarios where a **complete rewrite of an existing file is intentionally required**. If the file exists, it will be overwritten. If it doesn't exist, it will be created. This tool will automatically create any directories needed to write the file.

⚠️ **REQUIRED PARAMETERS - ALL THREE MUST BE PROVIDED**:
- path: (required) The path of the file to write to (relative to the current workspace directory ${args.cwd})
- content: (required) The content to write to the file. When performing a full rewrite of an existing file or creating a new one, **ALWAYS provide the COMPLETE intended content** of the file. You MUST include ALL parts of the file, even if they haven't been modified. Do NOT include line numbers in the content.
- line_count: (required) **CRITICAL - DO NOT FORGET THIS PARAMETER**
  - You MUST count and provide the exact total number of lines in your content
  - Include ALL lines: code, empty lines, and comments
  - This parameter is mandatory and cannot be omitted
  - Incorrect line count will cause file corruption
  - **PERFORMANCE RECOMMENDATION**: Try to keep line_count under 300 lines when possible for optimal processing efficiency


Usage:
<use_write_file>
<path>File path here</path>
<content>
Line 1 of your content
Line 2 of your content
Line 3 of your content
</content>
<line_count>3</line_count>  <!-- Always count and provide the exact number of lines -->
</use_write_file>

Example: Requesting to write to frontend-config.json
<use_write_file>
<path>frontend-config.json</path>
<content>
{
  "apiEndpoint": "https://api.example.com",
  "theme": {
    "primaryColor": "#007bff",
    "secondaryColor": "#6c757d",
    "fontFamily": "Arial, sans-serif"
  },
  "features": {
    "darkMode": true,
    "notifications": true,
    "analytics": false
  },
  "version": "1.0.0"
}
</content>
<line_count>14</line_count>
</use_write_file>`;
}
