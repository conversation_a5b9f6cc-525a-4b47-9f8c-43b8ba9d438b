import { ToolArgs } from './types';

export function getUseReplaceFileDescription(args: ToolArgs, isSearchReplace?: boolean) {
  if (isSearchReplace) {
    return `## use_replace_file
Description: Find and replace text strings or regex patterns within a file. This tool performs targeted replacements across multiple locations and shows a diff preview before applying changes. Ideal for code refactoring, content updates, and batch text modifications.

Required Parameters:
- path: File path relative to current workspace directory (${args.cwd.toPosix()})
- search: The text or pattern to search for. MUST be the exact literal text from the file content.
- replace: The text to replace matches with. MUST be the exact literal text for the file content.

**CRITICAL WARNING - AVOID XML TAG CONTAMINATION:**
- NEVER include XML tool tags (like <use_replace_file> or </use_replace_file>) in search or replace parameters
- The XML tags are ONLY for tool invocation structure, NOT file content
- Only put actual source code or file text content in search and replace parameters
- Mixing tool tags with file content will corrupt the target file

Optional Parameters:
- start_line: Start line number for range-limited replacement (1-based, inclusive)
- end_line: End line number for range-limited replacement (1-based, inclusive)
- use_regex: Enable regex pattern matching ("true" or "false", default: "false")
- ignore_case: Enable case-insensitive matching ("true" or "false", default: "true")

Important Notes:
- **For files over 300 lines**: Use start_line/end_line parameters to target specific sections instead of replacing entire code blocks
- When use_regex="true": search parameter is treated as a regular expression with full regex syntax support
- When ignore_case="true": matching ignores letter case in both literal and regex modes
- Replacement text supports regex capture groups ($1, $2, etc.) when use_regex="true"
- Tool shows diff preview before applying changes for verification

**Critical Guidelines for Large Content:**
- **Search Content**: Keep search text concise but unique. If content is too long, use distinctive patterns or unique identifiers
- **Replace Content**: For large replacements, break into smaller chunks or use multiple operations
- **Content Preservation**: If search/replace content exceeds ~500 characters, consider using line ranges to limit scope
- **Verification Strategy**: Always use unique markers or patterns in search to ensure exact matching
- **Fallback Approach**: For very large content blocks, consider using use_write_file for complete file rewrites instead

Usage:

<use_replace_file>
<path>File path here</path>
<search>Text or pattern to search for</search>
<replace>Text to replace matches with</replace>
<start_line>Starting line number (optional)</start_line>
<end_line>Ending line number (optional)</end_line>
<use_regex>true/false (optional, default: false)</use_regex>
<ignore_case>true/false (optional, default: true)</ignore_case>
</use_replace_file>

**Examples:**
Note: In all examples below, the XML tags are tool invocation format only. The content inside <search> and <replace> tags represents actual file content.

1. Simple text replacement:
<use_replace_file>
<path>example.ts</path>
<search>oldText</search>
<replace>newText</replace>
</use_replace_file>

2. Regex pattern with capture groups:
<use_replace_file>
<path>example.ts</path>
<search>old(w+)</search>
<replace>new$1</replace>
<use_regex>true</use_regex>
<ignore_case>true</ignore_case>
</use_replace_file>

3. Range-limited replacement in large files:
<use_replace_file>
<path>example.ts</path>
<search>console.log(.*)</search>
<replace>// console.log(commented out)</replace>
<start_line>10</start_line>
<end_line>20</end_line>
<use_regex>true</use_regex>
</use_replace_file>

4. Handling large content with unique markers:
<use_replace_file>
<path>example.ts</path>
<search>// START: user authentication logic</search>
<replace>// START: updated user authentication logic</replace>
<start_line>50</start_line>
<end_line>100</end_line>
</use_replace_file>

5. Using distinctive patterns for precise matching:
<use_replace_file>
<path>example.ts</path>
<search>export class UserService {</search>
<replace>export class EnhancedUserService {</replace>
</use_replace_file>

**FINAL REMINDER:**
- XML tags like <use_replace_file>, <search>, <replace>, </use_replace_file> are TOOL STRUCTURE ONLY
- These tags must NEVER appear in actual file content
- Only put real source code, text, or file content inside the <search> and <replace> parameters
- Violating this rule will corrupt files and make them uneditable
`;
  }
  return ``;
}
