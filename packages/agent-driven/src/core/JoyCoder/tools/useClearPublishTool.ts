import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import fs from 'fs';
import open from 'open';
import FormData from 'form-data';
import { pinyin } from 'pinyin-pro';
import fetch from 'node-fetch';
import { JoyCoder } from '../../Joycoder';
import { ToolUse } from '../../assistant-message';
import { handleError, pushToolResult, removeClosingTag } from './common';
import { JoyCoderSayTool } from '../../../shared/ExtensionMessage';

async function runCommand(cmd: string, workingDir = process.cwd()) {
  try {
    const execAsync = promisify(exec);
    const { stdout, stderr } = await execAsync(cmd, { cwd: workingDir });
    return stdout + stderr;
  } catch (error) {
    // Specify the type of error
    return error.message;
  }
}

async function clearPublishTool(projectName: any, projectPath = process.cwd()) {
  const distPath = path.join(projectPath, 'dist');
  if (!fs.existsSync(distPath)) {
    return `部署失败！\n错误信息：${distPath ?? 'dist'} 目录不存在，请检查构建是否成功`;
  }
  const zipPath = path.join(projectPath, 'dist.zip');
  await runCommand(`zip -r dist.zip dist`, projectPath);
  const url = 'http://server-clear.jd.com/api/publishByIDE';
  const data = {
    name_en: 'open',
    env_type: 'test',
    env_name: `joycode-${projectName}`,
    desc: projectName,
  };
  const formData = new FormData();
  formData.append('file', fs.createReadStream(zipPath), 'dist.zip');
  formData.append('name_en', data.name_en);
  formData.append('env_type', data.env_type);
  formData.append('env_name', data.env_name);
  formData.append('desc', data.desc);
  const response = await fetch(url, {
    method: 'POST',
    body: formData,
  });
  if (response.status !== 200) {
    fs.unlinkSync(zipPath);
    return `部署失败！\n错误信息：上传接口返回状态码 ${response.status}`;
  }
  const responseData: any = await response.json();
  if (responseData.code !== 0) {
    fs.unlinkSync(zipPath);
    const formDataEntries = Object.entries(data).map(([key, value]) => `${key}: ${value}`);
    return `部署失败！\n错误信息：${responseData.message}，表单数据：${formDataEntries.join(', ')}`;
  }
  const fullUrl = `https://${responseData.data.url}`;
  await open(fullUrl);
  fs.unlinkSync(zipPath);
  return `部署成功！\n访问地址：${fullUrl}`;
}

export async function useClearPublishTool(joyCoder: JoyCoder, block: ToolUse) {
  let project_name: string | undefined = block.params.project_name;
  const project_path: string | undefined = block.params.project_path;
  const sharedMessageProps: JoyCoderSayTool = {
    tool: 'clearPublish',
    project_name: removeClosingTag(joyCoder, 'project_name', project_name, block.partial),
  };
  if (block.partial) {
    const partialMessage = JSON.stringify({
      ...sharedMessageProps,
      content: undefined,
    } as JoyCoderSayTool);
    await joyCoder.say('tool', partialMessage, undefined, block.partial);
    return;
  } else {
    try {
      if (!!project_name && !!project_path) {
        try {
          const chineseRegex = /[\u4E00-\u9FFF]/;
          if (chineseRegex.test(project_name)) {
            try {
              project_name = pinyin(project_name, { toneType: 'none', separator: '' });
            } catch (error) {
              project_name = Buffer.from(project_name).toString('base64').slice(0, 16) ?? project_name;
            }
          }
          const responseText = await clearPublishTool(project_name, project_path);
          const partialMessage = JSON.stringify({
            ...sharedMessageProps,
            content: responseText,
          } as JoyCoderSayTool);
          await joyCoder.say('tool', partialMessage, undefined, false);
          await pushToolResult(joyCoder, block, responseText);
        } catch (error) {
          await pushToolResult(joyCoder, block, '(use_clear_publish tool does not return anything)');
        }
      } else {
        await pushToolResult(
          joyCoder,
          block,
          await joyCoder.sayAndCreateMissingParamError('use_clear_publish', 'project_name', 'project_path')
        );
      }
      return;
    } catch (error) {
      await handleError(joyCoder, block, '联网搜索异常', error);
      return;
    }
  }
}
