import * as vscode from 'vscode';
import { McpSendMsgType, McpCommonSendMsgType } from '@joycoder/shared/src/mcp/McpTypes';
let settingWebview: vscode.WebviewPanel | undefined = undefined;
export async function notifyWebviewOfServerChanges(data?: any) {
  const type = data.type;
  switch (type) {
    case 'mcpServers':
      const message = {
        type: McpSendMsgType.GET_MCP_CONNECTION_SERVER,
      };
      await vscode.commands.executeCommand('JoyCode.config.setting.mcp', message);
      break;
    default:
      console.log('消息未匹配');
      break;
  }
}
