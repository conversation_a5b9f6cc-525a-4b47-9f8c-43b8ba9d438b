import { memo, useCallback, useMemo, useRef } from 'react';
import { JoyCoderMessage } from '../../../../src/shared/ExtensionMessage';
import { CopyButton } from '../common';
import { CheckmarkControlAdaptor, CheckmarkControlAdaptorRef } from '../../adaptor/components/CheckmarkControlAdaptor';
import { useExtensionState } from '../../context/ExtensionStateContext';
import styled from 'styled-components';
import { MessageUtils } from '../../../../src/shared/MessageUtils';

export interface MessageToolbarProps {
  shouldShow?: boolean;
  isGenerating?: boolean;
  allMessages?: (JoyCoderMessage | JoyCoderMessage[])[];
  messageIndex?: number;
  currentMessage?: JoyCoderMessage;
  /** 消息时间戳，用于CheckmarkControlAdaptor */
  messageTs?: number;
}

/**
 * 消息工具条组件
 * 提供回滚功能和用户消息复制功能
 */
export const MessageToolbar = memo(({
  shouldShow = true,
  isGenerating = false,
  allMessages,
  messageIndex,
  currentMessage,
  messageTs
}: MessageToolbarProps) => {
  const checkmarkControlRef = useRef<CheckmarkControlAdaptorRef>(null);
  const { isRemoteEnvironment } = useExtensionState();

  const previousMessage = MessageUtils.getPreviousMessage(allMessages, messageIndex);

  // 判断是否应该显示CheckmarkControlAdaptor（回滚按钮）
  const shouldShowCheckmarkControl = useCallback(() => {
    return MessageUtils.shouldShowCheckmarkControl(currentMessage, messageTs, isRemoteEnvironment);
  }, [currentMessage, messageTs, isRemoteEnvironment]);

  // 获取回滚按钮对应的消息时间戳
  const getCheckmarkMessageTs = useCallback(() => {
    return MessageUtils.getCheckmarkMessageTs(currentMessage, messageTs);
  }, [currentMessage, messageTs]);

  // 获取回滚按钮对应的checkpoint消息的上一条消息
  const getCheckmarkPreviousMessage = useCallback(() => {
    return MessageUtils.getCheckmarkPreviousMessage(currentMessage, previousMessage);
  }, [currentMessage, previousMessage]);

  // 处理撤回按钮点击
  const handleRollbackClick = useCallback(() => {
    checkmarkControlRef.current?.handleRollbackClick();
  }, []);

  // 获取按钮样式信息
  const getButtonInfo = useCallback(() => {
    const checkmarkPreviousMessage = getCheckmarkPreviousMessage();
    if (!checkmarkPreviousMessage) return { shouldShowTextButton: false, isDisabled: false };

    // 使用统一的MessageUtils判断逻辑
    const isUser = MessageUtils.isUserMessage(checkmarkPreviousMessage);
    const isCheckpointOrTransition = MessageUtils.isCheckpointMessage(checkmarkPreviousMessage);

    const shouldShowTextButton = !isUser && !isCheckpointOrTransition;

    return {
      shouldShowTextButton,
      isDisabled: checkmarkControlRef.current?.isDisabled || false
    };
  }, [getCheckmarkPreviousMessage]);






  // 判断是否应该显示复制按钮
  const shouldShowCopyButton = useCallback(() => {
    return MessageUtils.shouldShowCopyButton(currentMessage, previousMessage);
  }, [currentMessage, previousMessage]);

  // 获取用户消息文本内容
  const getUserMessageText = useCallback(() => {
    if (!currentMessage) return '';
    return MessageUtils.getUserMessageText(currentMessage, previousMessage);
  }, [currentMessage, previousMessage]);

  // 计算是否应该显示回滚按钮和复制按钮
  const showCopyButton = shouldShowCopyButton();
  const showCheckmarkControl = shouldShowCheckmarkControl();
  const checkmarkMessageTs = getCheckmarkMessageTs();

  // 使用 useMemo 来稳定布局计算，避免闪烁
  const alignLeft = useMemo(() => {
    return MessageUtils.shouldAlignToolbarLeft(currentMessage, previousMessage);
  }, [currentMessage, previousMessage]);

  // 检查数据是否准备完成，避免闪烁
  const isDataReady = useMemo(() => {
    return MessageUtils.isToolbarDataReady(currentMessage, showCheckmarkControl, allMessages, messageIndex);
  }, [currentMessage, showCheckmarkControl, allMessages, messageIndex]);

  // 渲染回滚按钮的通用函数
  const renderRollbackButton = useCallback((buttonInfo: { shouldShowTextButton: boolean; isDisabled: boolean }) => {
    if (!showCheckmarkControl || !checkmarkMessageTs) {
      return null;
    }

    return (
      <div style={{ position: 'relative' }}>
        {buttonInfo.shouldShowTextButton ? (
          <TextRollbackButton
            title="回滚代码文件及消息至此处"
            onClick={handleRollbackClick}
            disabled={buttonInfo.isDisabled}
          >
            <i
              className="icon iconfont icon-fanhuishangyibu"
              style={{
                fontSize: '11px',
                marginRight: '6px',
                color: 'var(--vscode-descriptionForeground)'
              }}
            />
            回滚到当前检查点
          </TextRollbackButton>
        ) : (
          <RollbackButton
            title="回滚代码文件及消息至此处"
            onClick={handleRollbackClick}
            disabled={buttonInfo.isDisabled}
          >
            <i
              className="icon iconfont icon-fanhuishangyibu"
              style={{
                fontSize: '14px',
                color: 'var(--vscode-descriptionForeground)'
              }}
            />
          </RollbackButton>
        )}
      </div>
    );
  }, [showCheckmarkControl, checkmarkMessageTs, handleRollbackClick]);

  // 如果不应该显示或者正在生成中，则返回null
  if (!shouldShow || isGenerating) {
    return null;
  }

  // 如果既不显示复制按钮也不显示回滚按钮，则返回null
  if (!showCopyButton && !showCheckmarkControl) {
    return null;
  }

  // 如果数据未准备好，返回null避免闪烁
  if (!isDataReady) {
    return null;
  }

  const buttonInfo = getButtonInfo();

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'flex-start',
        gap: '4px',
        marginTop: '4px',
        transition: 'opacity 0.2s',
        flexDirection: 'column'
      }}
      className="message-toolbar"
    >
      {/* 工具按钮容器 */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '4px',
        justifyContent: alignLeft ? 'flex-start' : 'flex-end',
        width: '100%'
      }}>
        {/* 当需要靠左排列时，回滚按钮在前 */}
        {alignLeft && renderRollbackButton(buttonInfo)}

        {/* 根据条件显示复制按钮 */}
        {showCopyButton && (
          <CopyButton text={getUserMessageText()} />
        )}

        {/* 当不需要靠左排列时，回滚按钮在后（保持原有顺序） */}
        {!alignLeft && renderRollbackButton(buttonInfo)}
      </div>

      {/* CheckmarkControlAdaptor 只用于确认对话框 */}
      {showCheckmarkControl && checkmarkMessageTs && (
        <CheckmarkControlAdaptor
          ref={checkmarkControlRef}
          messageTs={checkmarkMessageTs}
          previousMessage={getCheckmarkPreviousMessage()}
        />
      )}
    </div>
  );
});

MessageToolbar.displayName = 'MessageToolbar';

// 样式组件
const RollbackButton = styled.button`
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--vscode-descriptionForeground);
  transition: 0.2s;
  min-width: auto;
  min-height: auto;

  &:hover:not(:disabled) {
    background: var(--vscode-toolbar-hoverBackground);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const TextRollbackButton = styled.button`
  background: var(--vscode-button-secondaryBackground, #72747C);
  border: 1px solid var(--vscode-button-border);
  cursor: pointer;
  padding: 4px 6px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--vscode-button-secondaryForeground, #72747C);
  font-size: 11px;
  font-family: var(--vscode-font-family);
  white-space: nowrap;

  &:hover:not(:disabled) {
    background: var(--vscode-button-secondaryHoverBackground, #72747C);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;
