import { VSCodeBadge, VSCodeProgressRing } from '@vscode/webview-ui-toolkit/react';
import deepEqual from 'fast-deep-equal';
import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useEvent, useSize } from 'react-use';
import styled from 'styled-components';
import {
  JoyCoderApiReqInfo,
  JoyCoderAskUseMcpServer,
  JoyCoderMessage,
  JoyCoderSayTool,
  COMPLETION_RESULT_CHANGES_FLAG,
  ExtensionMessage,
  ButtonText,
  JoyCoderPlanModeResponse,
  JoyCoderAskQuestion,
  JoyCoderSayText,
} from '../../../../src/shared/ExtensionMessage';
import { COMMAND_OUTPUT_STRING, COMMAND_REQ_APP_STRING } from '../../../../src/shared/combineCommandSequences';
import { useExtensionState } from '../../context/ExtensionStateContext';
import { findMatchingResourceOrTemplate } from '../../utils/mcp';
import { vscode } from '../../utils/vscode';
import { CheckpointControls, CheckpointOverlay } from '../common/CheckpointControls';
import CodeAccordianAdaptor, { cleanPathPrefix } from '../../adaptor/components/CodeAccordianAdaptor';
import CodeBlock, { CODE_BLOCK_BG_COLOR } from '../common/CodeBlock';
import MarkdownBlock from '../common/MarkdownBlock';
import SuccessButton from '../common/SuccessButton';
import Thumbnails from '../common/Thumbnails';
import McpResourceRow from '../mcp/McpResourceRow';
import McpToolRow from '../mcp/McpToolRow';
import { highlightMentions } from '../../adaptor/components/TaskHeaderAdaptor';
import { OptionsButtons } from './OptionsButtons';
import NewTaskPreview from './NewTaskPreview';
import FileList from './FileList';
import { CommandExecution } from './CommandExecution';
import { safeJsonParse } from '../../utils/safeJsonParse';
import { FollowUpSuggest } from './FollowUpSuggest';
import { ActionType, IActionCustomReportParam } from '@joycoder/shared/src/report/ationType';
import { modes, getModeBySlug } from '../../utils/modes';
import ModeInfo from '../common/ModeInfo';
import { MessageToolbar } from '../chat/MessageToolbar';
import { FileIcon, ThinkingAnimation } from '../../components/common';
import CodebaseSearchResultsDisplay from './CodebaseSearchResultsDisplay';
import { MessageUtils } from '../../../../src/shared/MessageUtils';

const ChatRowContainer = styled.div<{ $isCheckpointMessage: boolean }>`
  padding: ${(props) => {
    // checkpoint_created消息使用特殊的padding
    if (props.$isCheckpointMessage) {
      return '0 6px 0 15px';
    }
    // 其他所有消息使用统一的padding
    return '10px 6px 0 15px';
  }};
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 8px;

  &:hover ${CheckpointControls} {
    opacity: 1;
  }
`;

const ContentContainer = styled.div`
  flex: 1;
  min-width: 0;
  position: relative;

  &:hover .message-toolbar {
    opacity: 1;
  }
`;

// 折叠文本组件
const CollapsibleText = memo(
  ({
    text,
    maxLines = 15,
    backgroundColor = 'var(--vscode-button-secondaryBackground)',
    textColor = 'var(--vscode-button-secondaryForeground)',
    align = 'left',
    onClose = () => {},
    showClose = false,
  }: {
    text: string;
    maxLines?: number;
    backgroundColor?: string;
    textColor?: string;
    align?: 'left' | 'right';
    onClose?: () => void;
    showClose?: boolean;
  }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const [showSeeMore, setShowSeeMore] = useState(false);
    const textContainerRef = useRef<HTMLDivElement>(null);
    const textRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      if (textRef.current && textContainerRef.current) {
        const { scrollHeight } = textRef.current;
        const { clientHeight } = textContainerRef.current;
        const isOverflowing = scrollHeight > clientHeight;
        setShowSeeMore(isOverflowing);
      }
    }, [text]);

    return (
      <>
        {/* {showClose && (

        )} */}
        <div
          style={{
            display: 'flex',
            justifyContent: align === 'right' ? 'flex-end' : 'flex-start',
            width: '100%',
          }}
        >
          <div
            ref={textContainerRef}
            style={{
              backgroundColor,
              color: textColor,
              borderRadius: '3px',
              padding: showSeeMore && !isExpanded ? '6px 8px 24px 8px' : showClose ? '6px 24px 6px 8px' : '6px 8px',
              whiteSpace: 'pre-line',
              wordWrap: 'break-word',
              position: 'relative',
              overflow: 'hidden',
              maxWidth: '100%',
              minWidth: 'auto',
            }}
          >
            <div>
              <div
                ref={textRef}
                style={{
                  display: '-webkit-box',
                  WebkitLineClamp: isExpanded ? 'unset' : maxLines,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  lineHeight: '18px',
                }}
              >
                {highlightMentions(text)}
              </div>
            </div>
            {!isExpanded && showSeeMore && (
              <div
                style={{
                  position: 'absolute',
                  left: 0,
                  right: 0,
                  bottom: 0,
                  display: 'flex',
                  alignItems: 'center',
                  cursor: 'pointer',
                }}
                onClick={() => setIsExpanded(true)}
              >
                <div
                  style={{
                    // color: 'var(--vscode-textLink-foreground)',
                    backgroundColor,
                    fontSize: 'inherit',
                    flex: 1,
                    textAlign: 'center',
                    height: '24px',
                    // background: `linear-gradient(180deg, rgba(32,32,35,0) 0%,rgba(32,32,35,1) 89.99999761581421%)`,
                    paddingTop: '4px',
                  }}
                >
                  <i className="icon iconfont icon-xiajiantou" />
                </div>
              </div>
            )}
            {isExpanded && (
              <div
                style={{
                  marginTop: '8px',
                  display: 'flex',
                  justifyContent: 'flex-end',
                  cursor: 'pointer',
                  width: '100%',
                }}
                onClick={() => setIsExpanded(false)}
              >
                <div
                  style={{
                    // color: 'var(--vscode-textLink-foreground)',
                    fontSize: 'inherit',
                    width: '100%',
                    textAlign: 'center',
                  }}
                >
                  <i className="icon iconfont icon-shangjiantou" />
                </div>
              </div>
            )}
          </div>
        </div>
      </>
    );
  },
);

interface ChatRowProps {
  message: JoyCoderMessage;
  isExpanded: boolean;
  onToggleExpand: () => void;
  lastModifiedMessage?: JoyCoderMessage;
  isLast: boolean;
  onHeightChange: (isTaller: boolean) => void;
  handleSecondaryButtonClick?: () => void;
  handlePrimaryButtonClick?: () => void;
  isStreaming?: boolean;
  buttonText?: ButtonText;
  buttonInfo?: Record<string, any>;
  onCopy?: () => void;
  currentModeSlug?: string;
  currentMode?: any; // 完整的模式对象，包含头像、名称等
  allMessages?: (JoyCoderMessage | JoyCoderMessage[])[];
  messageIndex?: number;
  onClose?: () => void;
}

interface ChatRowContentProps extends Omit<ChatRowProps, 'onHeightChange'> {
  isStreaming?: boolean;
}

const ChatRow = memo(
  (props: ChatRowProps) => {
    const {
      isLast,
      onHeightChange,
      message,
      lastModifiedMessage,
      currentModeSlug,
      currentMode,
      allMessages,
      messageIndex,
    } = props;
    // 存储前一个高度以便与当前高度进行比较
    // 这允许我们检测变化而不会导致重新渲染
    const prevHeightRef = useRef(0);

    // 使用ref存储currentMode以避免它成为依赖项
    const currentModeRef = useRef(currentMode);
    currentModeRef.current = currentMode;

    // 获取模式信息并确定这是否是用户消息以及是否应该显示头像
    const { displayMode, shouldShowAvatar } = useMemo(() => {
      // 使用新的简化头像显示逻辑
      const shouldShow = MessageUtils.shouldShowAvatar(message, allMessages, messageIndex);

      // 使用MessageUtils统一判断消息类型
      const isUser = MessageUtils.isUserMessage(message);
      const isCheckpoint = MessageUtils.isCheckpointMessage(message);

      if (isCheckpoint) {
        return {
          displayMode: null,
          shouldShowAvatar: false,
        };
      }

      if (isUser) {
        // 对于用户消息，不显示头像
        return {
          displayMode: {
            name: '用户',
            agentId: 'user',
          },
          shouldShowAvatar: shouldShow,
        };
      } else {
        // 优先级：1. 消息存储的模式信息，2. 传递的currentMode，3. 从slug获取
        let mode;
        if (message.modeInfo) {
          // 使用消息中存储的模式信息（保留历史模式）
          mode = {
            agentId: message.modeInfo.agentId,
            name: message.modeInfo.name,
          };
        } else {
          // 对于没有存储模式信息的消息，回退到当前模式
          mode = currentModeRef.current || (currentModeSlug ? getModeBySlug(currentModeSlug) || modes[0] : modes[0]);
        }

        // 确保模式具有正确的属性
        const processedMode = {
          ...mode,
          // 确保名称存在以供回退
          name: mode.name || '智能体',
          // 确保agentId存在
          agentId: mode.agentId || 'chat',
        };

        return {
          displayMode: processedMode,
          shouldShowAvatar: shouldShow,
        };
      }
    }, [currentModeSlug, message, allMessages, messageIndex]);

    // 使用统一的MessageUtils判断是否显示检查点
    const shouldShowCheckpoints = MessageUtils.shouldShowCheckpoints(message, isLast, lastModifiedMessage);

    const [chatrow, { height }] = useSize(
      <ChatRowContainer
        $isCheckpointMessage={MessageUtils.isCheckpointMessage(message)}
      >
        {shouldShowAvatar && displayMode && (
          <ModeInfo name={displayMode.name} agentId={displayMode.agentId} showAvatar={true} />
        )}
        <ContentContainer>
          <ChatRowContent {...props} />
        </ContentContainer>
        {shouldShowCheckpoints && <CheckpointOverlay messageTs={message.ts} />}
      </ChatRowContainer>,
    );

    useEffect(() => {
      // 用于部分内容、命令输出等
      // 注意：这里不区分部分或完整很重要，因为我们在chatview中的滚动效果需要处理从部分到完整的高度变化
      const isInitialRender = prevHeightRef.current === 0; // 防止添加新元素时滚动，因为我们已经为此滚动了
      // 高度从无穷大开始
      if (isLast && height !== 0 && height !== Infinity && height !== prevHeightRef.current) {
        if (!isInitialRender) {
          onHeightChange(height > prevHeightRef.current);
        }
        prevHeightRef.current = height;
      }
    }, [height, isLast, onHeightChange, message]);

    // 我们不能返回null，因为virtuoso不支持它，所以我们使用单独的visibleMessages数组来过滤不应该渲染的消息
    return chatrow;
  },
  // memo进行浅比较，所以我们需要对可能改变属性的数组/对象进行深比较
  deepEqual,
);

export default ChatRow;

// 无工作空间提示组件
const NoWorkspacePrompt = memo(() => {
  const handleOpenFolder = useCallback(() => {
    vscode.postMessage({
      type: 'openFolder',
    });
  }, []);

  return (
    <div
      style={{
        padding: '8px 12px',
        backgroundColor: 'var(--vscode-editor-background)',
        borderRadius: '1px 6px 6px 6px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-end',
      }}
    >
      {/* 提示文本 */}
      <div
        style={{
          fontSize: '14px',
          lineHeight: '20px',
          marginBottom: '12px',
          color: 'var(--vscode-editor-foreground)',
          width: '100%',
        }}
      >
        请打开文件夹以保存生成的代码内容
      </div>

      {/* 打开文件夹按钮 */}
      <button
        onClick={handleOpenFolder}
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '4px',
          padding: '8px 9px',
          backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
          color: 'var(--vscode-button-secondaryForeground, #72747c)',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '12px',
          fontFamily: 'var(--vscode-font-family)',
          transition: 'background-color 0.2s',
          width: '96px',
          border: 'none',
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.opacity = '0.8';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.opacity = '1';
        }}
      >
        <i
          className="icon iconfont icon-wenjianjiadakai"
          style={{
            fontSize: '14px',
          }}
        />
        打开文件夹
      </button>
    </div>
  );
});

NoWorkspacePrompt.displayName = 'NoWorkspacePrompt';

export const ChatRowContent = ({
  message,
  isExpanded,
  onToggleExpand,
  lastModifiedMessage,
  isLast,
  onCopy,
  allMessages,
  messageIndex,
  onClose,
}: ChatRowContentProps) => {
  const { mcpServers } = useExtensionState();

  // 判断消息是否正在生成中
  const isMessageGenerating = useMemo(() => {
    return MessageUtils.isMessageGenerating(message, isLast, lastModifiedMessage);
  }, [message, isLast, lastModifiedMessage]);

  // 简化工具条显示逻辑，使用统一的MessageUtils判断
  const shouldShowToolbar = useMemo(() => {
    return MessageUtils.shouldShowToolbar(message);
  }, [message]);

  const [seeNewChangesDisabled, setSeeNewChangesDisabled] = useState(false);
  // const [lastOne, setLastOne] = useState(false);

  const [cost, apiReqCancelReason, apiReqStreamingFailedMessage] = useMemo(() => {
    if (message.text != null && message.say === 'api_req_started') {
      const info: JoyCoderApiReqInfo = JSON.parse(message.text);
      return [info.cost, info.cancelReason, info.streamingFailedMessage];
    }
    return [undefined, undefined, undefined];
  }, [message.text, message.say]);

  // 检查是否存在reasoning消息，如果存在则隐藏api_req_started的思考动画
  const hasReasoningMessage = useMemo(() => {
    return MessageUtils.hasReasoningMessage(allMessages, messageIndex);
  }, [allMessages, messageIndex]);
  // 恢复任务时，最后一条不会是api_req_failed而是resume_task消息，所以api_req_started会显示加载旋转器。这就是为什么我们只是删除最后一个没有流式传输任何内容就失败的api_req_started
  const apiRequestFailedMessage =
    isLast && lastModifiedMessage?.ask === 'api_req_failed' // 如果请求被重试，那么最新的消息是api_req_retried
      ? lastModifiedMessage?.text
      : undefined;
  const isCommandExecuting =
    isLast &&
    (lastModifiedMessage?.ask === 'command' || lastModifiedMessage?.say === 'command') &&
    lastModifiedMessage?.text?.includes(COMMAND_OUTPUT_STRING);

  const isMcpServerResponding = isLast && lastModifiedMessage?.say === 'mcp_server_request_started';

  const type = message.type === 'ask' ? message.ask : message.say;

  const normalColor = 'var(--vscode-foreground)';
  // const errorColor = 'var(--vscode-errorForeground)';
  const errorColor = 'var(--vscode-editorWarning-foreground)';
  const warnColor = 'var(--vscode-foreground)';
  // const successColor = 'var(--vscode-charts-green)';
  const infoColor = 'var(--vscode-charts-blue)';
  const cancelledColor = 'var(--vscode-descriptionForeground)';
  const headerClasses = 'flex items-center gap-[10px] mb-[10px] break-words';

  const handleMessage = useCallback((event: MessageEvent) => {
    const message: ExtensionMessage = event.data;
    switch (message.type) {
      case 'relinquishControl': {
        setSeeNewChangesDisabled(false);
        break;
      }
    }
  }, []);

  useEvent('message', handleMessage);

  const [icon, title] = useMemo(() => {
    switch (type) {
      case 'error':
        return [
          <span
            className="codicon codicon-warning"
            style={{
              color: warnColor,
              marginBottom: '-1.5px',
            }}
          ></span>,
          <span style={{ color: warnColor, fontWeight: 'bold' }}>提示</span>,
        ];
      case 'mistake_limit_reached':
        return [
          <span
            className="codicon codicon-warning"
            style={{
              color: warnColor,
              marginBottom: '-1.5px',
            }}
          ></span>,
          <span style={{ color: warnColor, fontWeight: 'bold' }}>JoyCode 遇到问题了...</span>,
        ];
      case 'auto_approval_max_req_reached':
        return [
          <span
            className="codicon codicon-warning"
            style={{
              color: warnColor,
              marginBottom: '-1.5px',
            }}
          ></span>,
          <span style={{ color: warnColor, fontWeight: 'bold' }}>已达到最大请求数</span>,
        ];
      case 'command':
        return [
          isCommandExecuting ? (
            <div
              style={{
                margin: '10px 0 0 10px',
              }}
            >
              <ThinkingAnimation />
            </div>
          ) : (
            <span
              className="codicon codicon-terminal"
              style={{
                color: normalColor,
                margin: '10px 0 0 10px',
              }}
            ></span>
          ),
          <span style={{ color: normalColor, fontWeight: 'bold' }}>JoyCode 想要执行这个命令:</span>,
        ];
      case 'use_mcp_server':
        const mcpServerUse = JSON.parse(message.text || '{}') as JoyCoderAskUseMcpServer;
        return [
          isMcpServerResponding ? (
            <div
              style={{
                margin: '10px 0 0 10px',
              }}
            >
              <ThinkingAnimation />
            </div>
          ) : (
            <span
              className="codicon codicon-server"
              style={{
                color: normalColor,
                marginBottom: '-1.5px',
              }}
            ></span>
          ),
          <span style={{ color: normalColor, fontWeight: 'bold' }}>
            JoyCode 想要在 <code>{mcpServerUse.serverName}</code> MCP 服务器上
            {mcpServerUse.type === 'use_mcp_tools' ? '使用一个工具' : '访问一个资源'}：
          </span>,
        ];
      case 'completion_result':
        return [
          <span
            className="codicon codicon-check hidden"
            style={{
              color: infoColor,
              marginBottom: '-1.5px',
              display: 'none',
            }}
          ></span>,
          <span style={{ color: infoColor, fontWeight: 'bold' }} className="hidden">
            任务结束
          </span>,
        ];

      case 'followup':
        return [
          <span
            className="codicon codicon-question"
            style={{
              color: normalColor,
              marginBottom: '-1.5px',
            }}
          ></span>,
          <span style={{ color: normalColor, fontWeight: 'bold' }}>JoyCode 有一个问题:</span>,
        ];
      default:
        return [null, null];
    }
  }, [type, isCommandExecuting, isMcpServerResponding, message.text]);

  const headerStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
    margin: '12px 0',
  };

  const pStyle: React.CSSProperties = {
    margin: 0,
    whiteSpace: 'pre-wrap',
    wordBreak: 'break-word',
    overflowWrap: 'anywhere',
  };

  const tool: any = useMemo(() => {
    if (message.ask === 'tool' || message.say === 'tool') {
      return JSON.parse(message.text || '{}') as JoyCoderSayTool;
    }
    return null;
  }, [message.ask, message.say, message.text]);

  const followUpData = useMemo(() => {
    if (message.type === 'ask' && message.ask === 'followup' && !message.partial) {
      return safeJsonParse<any>(message.text);
    }
    return null;
  }, [message.type, message.ask, message.partial, message.text]);

  const toolIcon = (name: string) => (
    <span
      className={`icon iconfont icon-${name}`}
      style={{
        color: 'var(--vscode-foreground)',
        marginRight: '4px',
      }}
    ></span>
  );
  const [hasChanges, setHasChanges] = useState(false);
  const messageText = useMemo(() => {
    if (message.say !== 'text' && message.say !== 'completion_result') {
      setHasChanges(false);
      return null;
    }
    let jsonaToParse = message.text;
    if (message.say === 'completion_result' && jsonaToParse?.endsWith(COMPLETION_RESULT_CHANGES_FLAG)) {
      setHasChanges(true);
      jsonaToParse = jsonaToParse.slice(0, -COMPLETION_RESULT_CHANGES_FLAG.length);
    } else {
      setHasChanges(false);
    }
    try {
      return JSON.parse(jsonaToParse || '{}') as JoyCoderSayText;
    } catch (e) {
      return { text: jsonaToParse } as JoyCoderSayText;
    }
  }, [message.say, message.text]);

  const { customModes } = useExtensionState();

  const reportAction = (data: IActionCustomReportParam) => {
    vscode.postMessage({
      type: 'chatgpt-webview-report',
      reportData: data,
    });
  };
  const handleCopy = useCallback(() => {
    if (!messageText || !messageText.taskId || !messageText.conversationId || !messageText.sessionId) return;
    reportAction({
      actionCate: 'ai',
      accept: 1,
      actionType: ActionType.copy,
      result: window.getSelection()?.toString(),
      conversationId: messageText.conversationId,
      extendMsg: {
        taskId: messageText.taskId,
        sessionId: messageText.sessionId,
      },
    });
  }, [messageText]);

  if (tool) {
    switch (tool.tool) {
      case 'appliedDiff':
      case 'editedExistingFile':
        return (
          <>
            <div style={headerStyle}>
              {toolIcon(tool.tool === 'appliedDiff' ? 'ziyuanchajian' : 'a-bianjiAi')}
              <span style={{ fontWeight: 'bold' }}>JoyCode 想要编辑这个文件 :</span>
            </div>
            <CodeAccordianAdaptor
              progressStatus={message.progressStatus}
              icon={toolIcon(tool.tool === 'appliedDiff' ? 'ziyuanchajian' : 'a-bianjiAi')}
              code={tool.diff || tool.content}
              diff={tool.diff || tool.content}
              path={tool.path!}
              isExpanded={isExpanded}
              onToggleExpand={onToggleExpand}
            />
            {/* 为工具调用消息添加工具条 */}
            <div style={{ position: 'relative' }}>
              <MessageToolbar
                shouldShow={shouldShowToolbar}
                isGenerating={isMessageGenerating}
                allMessages={allMessages}
                messageIndex={messageIndex}
                currentMessage={message}
                messageTs={message.ts}
              />
            </div>
          </>
        );
      case 'insertContent':
        return (
          <>
            <div className={headerClasses}>
              {toolIcon('daimaguanliqi')}
              <span className="font-bold">
                {tool.isOutsideWorkspace
                  ? '想要在工作区外进行编辑'
                  : tool.lineNumber === 0
                    ? '想要在末尾插入'
                    : `想要在第 ${tool.lineNumber} 行插入`}
              </span>
            </div>
            <CodeAccordianAdaptor
              icon={toolIcon('daimaguanliqi')}
              isLoading={message.partial}
              diff={tool.diff!}
              code={tool.diff!}
              path={tool.path!}
              isExpanded={isExpanded}
              onToggleExpand={onToggleExpand}
            />
            {/* 为工具调用消息添加工具条 */}
            <div style={{ position: 'relative' }}>
              <MessageToolbar
                shouldShow={shouldShowToolbar}
                isGenerating={isMessageGenerating}
                allMessages={allMessages}
                messageIndex={messageIndex}
                currentMessage={message}
                messageTs={message.ts}
              />
            </div>
          </>
        );
      case 'searchAndReplace':
        return (
          <>
            <div style={headerStyle}>
              {toolIcon('genghuan')}
              <span style={{ fontWeight: 'bold' }}>
                {message.type === 'ask' ? 'JoyCode 想要搜索替换' : 'JoyCode 已完成搜索替换'}
              </span>
            </div>

            <CodeAccordianAdaptor
              progressStatus={message.progressStatus}
              // isLoading={message.partial}
              code={tool.diff!}
              diff={tool.diff!}
              path={tool.path!}
              isExpanded={isExpanded}
              onToggleExpand={onToggleExpand}
            />
            {/* 为工具调用消息添加工具条 */}
            <div style={{ position: 'relative' }}>
              <MessageToolbar
                shouldShow={shouldShowToolbar}
                isGenerating={isMessageGenerating}
                allMessages={allMessages}
                messageIndex={messageIndex}
                currentMessage={message}
                messageTs={message.ts}
              />
            </div>
          </>
        );
      case 'codebaseSearch': {
        return (
          <div style={headerStyle}>
            {toolIcon('search')}
            <span style={{ fontWeight: 'bold' }}>
              {tool.path ? (
                <span>
                  JoyCode 需要在 <code>{tool.path}</code> 中搜索: <code>{tool.query}</code>
                </span>
              ) : (
                <span>
                  JoyCode 需要搜索代码库: <code>{tool.query}</code>
                </span>
              )}
            </span>
          </div>
        );
      }
      case 'newFileCreated':
        return (
          <>
            <div style={headerStyle}>
              {toolIcon('bianji')}
              <span style={{ fontWeight: 'bold' }}>JoyCode 想要创建新文件:</span>
            </div>
            <CodeAccordianAdaptor
              icon={toolIcon('bianji')}
              isLoading={message.partial}
              code={tool.content!}
              path={tool.path!}
              isExpanded={isExpanded}
              onToggleExpand={onToggleExpand}
            />
            {/* 为工具调用消息添加工具条 */}
            <div style={{ position: 'relative' }}>
              <MessageToolbar
                shouldShow={shouldShowToolbar}
                isGenerating={isMessageGenerating}
                allMessages={allMessages}
                messageIndex={messageIndex}
                currentMessage={message}
                messageTs={message.ts}
              />
            </div>
          </>
        );
      case 'readFile':
        return (
          <>
            <div
              style={{
                borderRadius: 3,
                backgroundColor: CODE_BLOCK_BG_COLOR,
                overflow: 'hidden',
                border: '1px solid var(--vscode-editorGroup-border)',
                margin: '10px 0',
                // paddingBottom: !isStreaming ? '50px' : '0',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '9px 10px',
                  cursor: 'pointer',
                  userSelect: 'none',
                  WebkitUserSelect: 'none',
                  MozUserSelect: 'none',
                  msUserSelect: 'none',
                }}
                onClick={() => {
                  vscode.postMessage({
                    type: 'openFile',
                    text: tool.content,
                  });
                }}
              >
                <FileIcon path={tool.path} />
                {tool.path?.startsWith('.') && <span>.</span>}
                <span
                  style={{
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    marginRight: '8px',
                    direction: 'rtl',
                    textAlign: 'left',
                  }}
                >
                  {cleanPathPrefix(tool.path ?? '') + '\u200E'}
                </span>
                <div style={{ flexGrow: 1 }}></div>
                <span
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  <i className="icon iconfont icon-a-zhankaishangxia" />
                </span>
              </div>
            </div>
            {/* 为工具调用消息添加工具条 */}
            <div style={{ position: 'relative' }}>
              <MessageToolbar
                shouldShow={shouldShowToolbar}
                isGenerating={isMessageGenerating}
                allMessages={allMessages}
                messageIndex={messageIndex}
                currentMessage={message}
                messageTs={message.ts}
              />
            </div>
          </>
        );
      case 'fetchInstructions':
        return (
          <>
            <div className={headerClasses}>
              {toolIcon('jisuanjiyuyan')}
              <span className="font-bold">想要获取</span>
            </div>
            <CodeAccordianAdaptor
              isLoading={message.partial}
              code={tool.content!}
              isExpanded={isExpanded}
              onToggleExpand={onToggleExpand}
            />
          </>
        );
      case 'listFilesTopLevel':
        return (
          <>
            <div style={headerStyle}>
              {toolIcon('wenjianjiadakai')}
              <span style={{ fontWeight: 'bold' }}>
                {message.type === 'ask' ? 'JoyCode 想要查看此目录中的顶层文件:' : 'JoyCode 查看了该目录下的顶层文件:'}
              </span>
            </div>
            <CodeAccordianAdaptor
              icon={toolIcon('wenjianjiadakai')}
              code={tool.content!}
              path={tool.path!}
              language="shell-session"
              isExpanded={isExpanded}
              onToggleExpand={onToggleExpand}
            />
          </>
        );
      case 'listFilesRecursive':
        return (
          <>
            <div style={headerStyle}>
              {toolIcon('wenjianjiadakai')}
              <span style={{ fontWeight: 'bold' }}>
                {message.type === 'ask'
                  ? 'JoyCode 查看了该目录下的顶层文件:'
                  : 'JoyCode 递归地查看了该目录中的所有文件:'}
              </span>
            </div>
            <CodeAccordianAdaptor
              icon={toolIcon('wenjianjiadakai')}
              code={tool.content!}
              path={tool.path!}
              language="shell-session"
              isExpanded={isExpanded}
              onToggleExpand={onToggleExpand}
            />
          </>
        );
      case 'listCodeDefinitionNames':
        return (
          <>
            <div style={headerStyle}>
              {toolIcon('jisuanjiyuyan')}
              <span style={{ fontWeight: 'bold' }}>
                {message.type === 'ask'
                  ? 'JoyCode 想要查看在这个目录中使用的源代码定义名称:'
                  : 'JoyCode 查看了该目录中使用的源代码定义名称:'}
              </span>
            </div>
            <CodeAccordianAdaptor
              icon={toolIcon('jisuanjiyuyan')}
              code={tool.content!}
              path={tool.path!}
              isExpanded={isExpanded}
              onToggleExpand={onToggleExpand}
            />
          </>
        );
      case 'searchFiles':
        return (
          <>
            <div style={headerStyle}>
              {toolIcon('sousuo')}
              <span style={{ fontWeight: 'bold' }}>
                JoyCode 尝试在该目录下搜索<code>{tool.regex}</code>:
              </span>
            </div>
            <CodeAccordianAdaptor
              icon={toolIcon('sousuo')}
              code={tool.content!}
              path={tool.path! + (tool.filePattern ? `/(${tool.filePattern})` : '')}
              language="plaintext"
              isExpanded={isExpanded}
              onToggleExpand={onToggleExpand}
            />
          </>
        );
      case 'useCodeBase':
        return (
          tool.files &&
          tool.files.length > 0 && (
            <div>
              <FileList files={tool.files} />
            </div>
          )
        );
      case 'newTask':
        return (
          <>
            <div style={headerStyle}>
              {toolIcon('guanlixiangmu')}
              <span style={{ fontWeight: 'bold' }}>
                JoyCode想在<code>{getModeBySlug(tool.mode, customModes)?.name ?? tool.mode}</code>模式下创建新子任务:
              </span>
            </div>
            <div
              style={{
                marginTop: '4px',
                backgroundColor: 'var(--vscode-button-secondaryBackground)',
                border: '1px solid var(--vscode-button-secondaryBackground)',
                borderRadius: '4px 4px 0 0',
                overflow: 'hidden',
                marginBottom: '2px',
              }}
            >
              <div
                style={{
                  padding: '9px 10px 9px 14px',
                  backgroundColor: 'var(--vscode-button-secondaryBackground, #72747C)',
                  borderBottom: '1px solid var(--vscode-editorGroup-border)',
                  fontWeight: 'bold',
                  fontSize: 'var(--vscode-font-size)',
                  color: 'var(--vscode-button-secondaryForeground, #72747C)',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                }}
              >
                {tool.partial ? ProgressIndicator() : <span className="codicon codicon-arrow-right"></span>}
                子任务内容
              </div>
              <div style={{ padding: '12px 16px', backgroundColor: 'var(--vscode-editor-background)' }}>
                {/* {tool.partial && <span style={{ marginBottom: '4px' }}>
                  <ThinkingAnimation />
                </span>} */}
                <MarkdownBlock markdown={tool.content} />
              </div>
            </div>
          </>
        );
      case 'switchMode':
        return (
          <>
            <div className={headerClasses}>
              {toolIcon('genghuan')}
              <span className="font-bold">
                {tool?.reason ? (
                  <>
                    JoyCode 想要切换到 <code>{getModeBySlug(tool?.mode, customModes)?.name ?? tool?.mode}</code>{' '}
                    模式,因为 {tool.reason}
                  </>
                ) : (
                  <>
                    JoyCode 想要切换到 <code>{getModeBySlug(tool?.mode, customModes)?.name ?? tool?.mode}</code> 模式
                  </>
                )}
              </span>
            </div>
          </>
        );
      case 'finishTask':
        return (
          <>
            <div style={headerStyle}>
              {toolIcon('wancheng')}
              <span style={{ fontWeight: 'bold' }}>JoyCode想完成此子任务</span>
            </div>
            <div
              style={{
                marginTop: '4px',
                backgroundColor: 'var(--vscode-button-secondaryBackground, #72747C)',
                border: '1px solid var(--vscode-button-secondaryBackground, #72747C)',
                borderRadius: '4px',
                overflow: 'hidden',
                marginBottom: '8px',
              }}
            >
              <div
                style={{
                  padding: '9px 10px 9px 14px',
                  backgroundColor: 'var(--vscode-button-secondaryBackground, #72747C)',
                  borderBottom: '1px solid var(--vscode-editorGroup-border)',
                  fontWeight: 'bold',
                  fontSize: 'var(--vscode-font-size)',
                  color: 'var(--vscode-button-secondaryForeground, #72747C)',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                }}
              >
                <span className="codicon codicon-check"></span>
                子任务已完成
              </div>
              <div style={{ padding: '12px 16px', backgroundColor: 'var(--vscode-editor-background)' }}>
                <MarkdownBlock markdown="子任务已完成！您可以查看结果并提出修改或下一步建议。如果一切正常，请确认以将结果返回给主任务。" />
              </div>
            </div>
          </>
        );
      case 'clearPublish':
        return (
          tool.project_name && (
            <div
              style={{
                borderRadius: 3,
                backgroundColor: CODE_BLOCK_BG_COLOR,
                overflow: 'hidden',
                border: '1px solid var(--vscode-editorGroup-border)',
                margin: '10px 0',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '9px 10px',
                  userSelect: 'none',
                  WebkitUserSelect: 'none',
                  MozUserSelect: 'none',
                  msUserSelect: 'none',
                }}
              >
                {toolIcon('fenxiang')}
                {!tool.content && (
                  <span
                    style={{
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      marginRight: '8px',
                      direction: 'rtl',
                      textAlign: 'left',
                    }}
                  >
                    正在发布{tool.project_name}项目
                  </span>
                )}
                {tool.content && (
                  <span
                    style={{
                      overflow: 'hidden',
                      marginRight: '8px',
                      direction: 'rtl',
                      textAlign: 'left',
                    }}
                  >
                    {tool.content}
                  </span>
                )}
                <div style={{ flexGrow: 1 }}></div>
              </div>
            </div>
          )
        );
      case 'webSearch':
        return (
          tool.query && (
            <div
              style={{
                borderRadius: 3,
                backgroundColor: CODE_BLOCK_BG_COLOR,
                overflow: 'hidden',
                border: '1px solid var(--vscode-editorGroup-border)',
                margin: '10px 0',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '9px 10px',
                  userSelect: 'none',
                  WebkitUserSelect: 'none',
                  MozUserSelect: 'none',
                  msUserSelect: 'none',
                }}
              >
                {toolIcon('lianwangsousuo')}
                <span
                  style={{
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    marginRight: '8px',
                    direction: 'rtl',
                    textAlign: 'left',
                  }}
                >
                  联网搜索：{tool.query}
                </span>
                <div style={{ flexGrow: 1 }}></div>
              </div>
            </div>
          )
        );
      default:
        return null;
    }
  }

  if (message.ask === 'command' || message.say === 'command') {
    const splitMessage = (text: string) => {
      const outputIndex = text.indexOf(COMMAND_OUTPUT_STRING);
      if (outputIndex === -1) {
        return { command: text, output: '' };
      }
      return {
        command: text.slice(0, outputIndex).trim(),
        output: text
          .slice(outputIndex + COMMAND_OUTPUT_STRING.length)
          .trim()
          .split('')
          .map((char) => {
            switch (char) {
              case '\t':
                return '→   ';
              case '\b':
                return '⌫';
              case '\f':
                return '⏏';
              case '\v':
                return '⇳';
              default:
                return char;
            }
          })
          .join(''),
      };
    };

    const { command: rawCommand, output } = splitMessage(message.text || '');

    const requestsApproval = rawCommand.endsWith(COMMAND_REQ_APP_STRING);
    const command = requestsApproval ? rawCommand.slice(0, -COMMAND_REQ_APP_STRING.length) : rawCommand;

    // setLastOne((message.conversationHistoryIndex ?? 0) === lastModifiedMessage?.conversationHistoryIndex);
    return (
      <>
        {/* <div style={headerStyle}>
          {icon}
          {title}
        </div> */}
        {/* <Terminal
					rawOutput={command + (output ? "\n" + output : "")}
					shouldAllowInput={!!isCommandExecuting && output.length > 0}
				/> */}
        <div
          style={{
            borderRadius: 3,
            border: '1px solid var(--vscode-editorGroup-border)',
            overflow: 'hidden',
            backgroundColor: CODE_BLOCK_BG_COLOR,
            margin: '10px 0',
          }}
        >
          <div
            style={{
              display: 'flex',
            }}
          >
            <span
              className="codicon codicon-terminal"
              style={{
                color: normalColor,
                margin: '10px 0 0 10px',
              }}
            ></span>
            <CodeBlock source={`${'```'}shell\n${command}\n${'```'}`} forceWrap={true} />
          </div>
          {output.length > 0 && (
            <div style={{ width: '100%' }}>
              <div
                onClick={onToggleExpand}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px',
                  width: '100%',
                  justifyContent: 'flex-start',
                  cursor: 'pointer',
                  padding: `2px 8px ${isExpanded ? 0 : 8}px 8px`,
                }}
              >
                <span className={`codicon codicon-chevron-${isExpanded ? 'down' : 'right'}`}></span>
                <span style={{ fontSize: '0.8em' }}>命令输出 </span>
              </div>
              {isExpanded && (
                <>
                  <CodeBlock source={`${'```'}shell\n${output}\n${'```'}`} />
                </>
              )}
            </div>
          )}
        </div>
        {requestsApproval && (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: 10,
              padding: 8,
              fontSize: '12px',
              color: 'var(--vscode-editorWarning-foreground)',
            }}
          >
            <i className="codicon codicon-warning"></i>
            <span>模型已确定此命令需要明确批准。</span>
          </div>
        )}
        {/* 为命令消息添加工具条 */}
        {message.text && (
          <div style={{ position: 'relative' }}>
            <MessageToolbar
              shouldShow={shouldShowToolbar}
              isGenerating={isMessageGenerating}
              allMessages={allMessages}
              messageIndex={messageIndex}
              currentMessage={message}
              messageTs={message.ts}
            />
          </div>
        )}
      </>
    );
  }

  if (message.ask === 'use_mcp_server' || message.say === 'use_mcp_server') {
    const useMcpServer = JSON.parse(message.text || '{}') as JoyCoderAskUseMcpServer;
    const server = mcpServers.find((server) => server.name === useMcpServer.serverName);
    return (
      <>
        <div style={headerStyle}>
          {icon}
          {title}
        </div>

        <div
          style={{
            background: 'var(--vscode-textCodeBlock-background)',
            borderRadius: '3px',
            padding: '8px 10px',
            marginTop: '8px',
          }}
        >
          {useMcpServer.type === 'get_mcp_resource' && (
            <McpResourceRow
              item={{
                // 使用匹配的资源/模板详细信息，带有回退
                ...(findMatchingResourceOrTemplate(
                  useMcpServer.uri || '',
                  server?.resources,
                  server?.resourceTemplates,
                ) || {
                  name: '',
                  mimeType: '',
                  description: '',
                }),
                // 总是使用请求中的实际URI
                uri: useMcpServer.uri || '',
              }}
            />
          )}

          {useMcpServer.type === 'use_mcp_tools' && (
            <>
              <div onClick={(e) => e.stopPropagation()}>
                <McpToolRow
                  tool={{
                    name: useMcpServer.toolName || '',
                    description: server?.tools?.find((tool) => tool.name === useMcpServer.toolName)?.description || '',
                    autoApprove:
                      server?.tools?.find((tool) => tool.name === useMcpServer.toolName)?.autoApprove || false,
                  }}
                  serverName={useMcpServer.serverName}
                />
              </div>
              {useMcpServer.arguments && useMcpServer.arguments !== '{}' && (
                <div style={{ marginTop: '8px' }}>
                  <div
                    style={{
                      marginBottom: '4px',
                      opacity: 0.8,
                      fontSize: '12px',
                      textTransform: 'uppercase',
                    }}
                  >
                    参数
                  </div>
                  <CodeAccordianAdaptor
                    code={useMcpServer.arguments}
                    language="json"
                    isExpanded={true}
                    onToggleExpand={onToggleExpand}
                  />
                </div>
              )}
            </>
          )}
        </div>
      </>
    );
  }

  switch (message.type) {
    case 'say':
      switch (message.say) {
        case 'api_req_started':
          // 如果存在reasoning消息，则完全隐藏api_req_started消息
          if (hasReasoningMessage) {
            return null;
          }

          // 内联定义图标和文案逻辑，直接在头像下方显示
          const getIconSpan = (iconName: string, color: string) => (
            <div
              style={{
                width: 16,
                height: 16,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <span
                className={`codicon codicon-${iconName} ${iconName === 'check' ? 'hidden' : ''}`}
                style={{
                  color,
                  fontSize: 16,
                  marginBottom: '-1.5px',
                  display: iconName === 'check' ? 'none' : 'inline-block',
                }}
              ></span>
            </div>
          );

          // 根据不同状态显示不同的图标和文案
          const apiIcon =
            apiReqCancelReason != null ? (
              apiReqCancelReason === 'user_cancelled' ? (
                getIconSpan('error', cancelledColor)
              ) : (
                getIconSpan('warning', normalColor)
              )
            ) : cost != null ? (
              getIconSpan('check', infoColor)
            ) : apiRequestFailedMessage ? (
              getIconSpan('warning', normalColor)
            ) : (
              // 请求中的时候，显示思考中动画
              <ThinkingAnimation />
            );

          const apiTitle =
            apiReqCancelReason != null ? (
              apiReqCancelReason === 'user_cancelled' ? (
                <span
                  style={{
                    color: normalColor,
                    fontWeight: 'bold',
                  }}
                >
                  API 请求已取消
                </span>
              ) : (
                <span
                  style={{
                    color: errorColor,
                    fontWeight: 'bold',
                  }}
                >
                  API 流式传输失败
                </span>
              )
            ) : cost != null ? (
              <span style={{ color: normalColor, fontWeight: 'bold' }} className="hidden">
                API 请求
              </span>
            ) : apiRequestFailedMessage ? (
              // 请求失败的时候，显示失败文案
              <span style={{ color: errorColor, fontWeight: 'bold' }}>API 请求失败</span>
            ) : (
              // 请求中的时候，显示加载文案
              <span style={{ color: normalColor, fontWeight: 'bold' }} className="hidden">
                API 请求中...
              </span>
            );

          return (
            <>
              <div
                style={{
                  marginBottom: (cost == null && apiRequestFailedMessage) || apiReqStreamingFailedMessage ? 10 : 0,
                  justifyContent: 'space-between',
                  userSelect: 'none',
                  WebkitUserSelect: 'none',
                  MozUserSelect: 'none',
                  msUserSelect: 'none',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                    marginBottom: '12px',
                  }}
                >
                  {apiIcon}
                  {apiTitle}
                  <VSCodeBadge
                    style={{
                      opacity: cost != null && cost > 0 ? 1 : 0,
                    }}
                  >
                    ${Number(cost || 0)?.toFixed(4)}
                  </VSCodeBadge>
                </div>
              </div>
              {((cost == null && apiRequestFailedMessage) || apiReqStreamingFailedMessage) && (
                <>
                  {(apiRequestFailedMessage || apiReqStreamingFailedMessage) && (
                    <p
                      style={{
                        ...pStyle,
                        color: 'var(--vscode-errorForeground)',
                      }}
                      dangerouslySetInnerHTML={{
                        __html: (apiRequestFailedMessage || apiReqStreamingFailedMessage) ?? '',
                      }}
                    ></p>
                  )}
                  {apiRequestFailedMessage?.toLowerCase().includes('powershell') && (
                    <p
                      style={{
                        ...pStyle,
                        color: 'var(--vscode-errorForeground)',
                      }}
                    >
                      {apiRequestFailedMessage?.toLowerCase().includes('powershell') && (
                        <>
                          <br />
                          <br />
                          看起来你遇到了 Windows PowerShell 的问题，请联系我们{' '}
                          <a
                            href="http://joycoder.jd.com/jdhopenplatform/JoyCoder/docs/contact"
                            style={{
                              color: 'inherit',
                              textDecoration: 'underline',
                            }}
                          >
                            故障排除指南
                          </a>
                          .
                        </>
                      )}
                    </p>
                  )}
                </>
              )}

              {isExpanded && (
                <div style={{ marginTop: '10px' }}>
                  <CodeAccordianAdaptor
                    code={JSON.parse(message.text || '{}').request}
                    language="markdown"
                    isExpanded={true}
                    onToggleExpand={onToggleExpand}
                  />
                </div>
              )}
            </>
          );
        case 'api_req_finished':
          return null; // 我们永远不应该看到这种消息类型
        case 'subtask_result':
          return (
            <div>
              <div
                style={{
                  marginTop: '0px',
                  backgroundColor: 'var(--vscode-button-secondaryBackground, #72747C)',
                  border: '1px solid var(--vscode-button-secondaryBackground, #72747C)',
                  borderRadius: '0 0 4px 4px',
                  overflow: 'hidden',
                  marginBottom: '8px',
                }}
              >
                <div
                  style={{
                    padding: '9px 10px 9px 14px',
                    backgroundColor: 'var(--vscode-button-secondaryBackground, #72747C)',
                    borderBottom: '1px solid var(--vscode-editorGroup-border)',
                    fontWeight: 'bold',
                    fontSize: 'var(--vscode-font-size)',
                    color: 'var(--vscode-button-secondaryForeground, #72747C)',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px',
                  }}
                >
                  <span className="codicon codicon-arrow-left"></span>
                  子任务结果内容
                </div>
                <div
                  style={{
                    padding: '12px 16px',
                    backgroundColor: 'var(--vscode-editor-background)',
                  }}
                >
                  <MarkdownBlock markdown={message.text} />
                </div>
              </div>
            </div>
          );

        // markdown文本内容
        case 'text':
          return (
            <div onCopy={handleCopy} style={{ position: 'relative' }}>
              <Markdown markdown={messageText?.text} />
              {messageText?.text && (
                <MessageToolbar
                  shouldShow={shouldShowToolbar}
                  isGenerating={isMessageGenerating}
                  allMessages={allMessages}
                  messageIndex={messageIndex}
                  currentMessage={message}
                  messageTs={message.ts}
                />
              )}
            </div>
          );
        // 深度思考
        case 'reasoning':
          // 判断reasoning消息是否已完成（非partial状态表示已完成）
          const isReasoningCompleted = !isMessageGenerating;

          return (
            <>
              {message.text && (
                <div
                  onClick={onToggleExpand}
                  style={{
                    overflow: 'hidden',
                  }}
                >
                  {/* 如果reasoning已完成且未展开，显示"已深度思考"样式 */}
                  {isReasoningCompleted && !isExpanded ? (
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <span
                        style={{
                          width: '95px',
                          height: '20px',
                          backgroundColor: 'var(--vscode-button-secondaryBackground, #72747C)',
                          borderRadius: '4px',
                          display: 'flex',
                          alignItems: 'center',
                          color: 'var(--vscode-button-secondaryForeground, #72747C)',
                          cursor: 'pointer',
                          marginBottom: '9px',
                        }}
                      >
                        <i
                          className="icon iconfont icon-shendusikao"
                          style={{ marginLeft: '5px', fontSize: '11px', height: '11px' }}
                        />
                        <span style={{ margin: '0 4px', fontSize: '11px', height: '11px' }}>已深度思考</span>
                        <i className="icon iconfont icon-youjiantou" style={{ fontSize: '14px', height: '15px' }} />
                      </span>
                    </div>
                  ) : !isReasoningCompleted && !isExpanded ? (
                    /* 如果reasoning正在进行中且未展开，显示思考动画 */
                    <div style={{ cursor: 'pointer' }}>
                      <span style={{ marginBottom: '4px' }}>
                        <ThinkingAnimation />
                      </span>
                      <span
                        style={{
                          color: 'var(--vscode-editor-foreground, #72747C)',
                          fontStyle: 'italic',
                          fontSize: '12px',
                          lineHeight: '18px',
                          display: 'block',
                          paddingLeft: '12px',
                          marginLeft: '8px',
                          borderLeft: '1px solid var(--vscode-button-secondaryBackground, #72747C)',
                        }}
                      >
                        {message.text}
                      </span>
                    </div>
                  ) : (
                    /* 展开状态显示完整的reasoning内容 */
                    <div>
                      <div style={{ cursor: 'pointer' }}>
                        {/* 根据reasoning是否完成显示不同的头部 */}
                        {isReasoningCompleted ? (
                          <span
                            style={{
                              width: '95px',
                              height: '20px',
                              backgroundColor: 'var(--vscode-button-secondaryBackground, #72747C)',
                              borderRadius: '4px',
                              display: 'flex',
                              alignItems: 'center',
                              color: 'var(--vscode-button-secondaryForeground, #72747C)',
                              cursor: 'pointer',
                              marginBottom: '9px',
                            }}
                          >
                            <i
                              className="icon iconfont icon-shendusikao"
                              style={{ marginLeft: '5px', fontSize: '11px', height: '11px' }}
                            />
                            <span style={{ margin: '0 4px', fontSize: '11px', height: '11px' }}>已深度思考</span>
                            <i className="icon iconfont icon-xiajiantou" style={{ fontSize: '14px', height: '15px' }} />
                          </span>
                        ) : (
                          <span style={{ marginBottom: '4px' }}>
                            <ThinkingAnimation />
                          </span>
                        )}
                      </div>
                      <span
                        style={{
                          color: 'var(--vscode-editor-foreground, #72747C)',
                          fontStyle: 'italic',
                          fontSize: '12px',
                          lineHeight: '18px',
                          display: 'block',
                          paddingLeft: '12px',
                          margin: `${isReasoningCompleted ? '8px 0 0 8px' : '0 0 0 8px'}`,
                          borderLeft: '1px solid var(--vscode-button-secondaryBackground, #72747C)',
                        }}
                      >
                        {message.text}
                      </span>
                    </div>
                  )}
                </div>
              )}
            </>
          );
        case 'user_feedback':
          return (
            <div style={{ position: 'relative' }}>
              <CollapsibleText
                text={message.text || ''}
                maxLines={15}
                align="right"
                onClose={onClose}
                showClose={messageIndex === 0}
              />
              {message.images && message.images.length > 0 && (
                <Thumbnails images={message.images} style={{ marginTop: '8px' }} />
              )}
              {message.text && (
                <MessageToolbar
                shouldShow={shouldShowToolbar}
                  isGenerating={isMessageGenerating}
                  allMessages={allMessages}
                  messageIndex={messageIndex}
                  currentMessage={message}
                  messageTs={message.ts}
                />
              )}
            </div>
          );
        case 'user_feedback_diff': {
          const tool = JSON.parse(message.text || '{}') as JoyCoderSayTool;
          return (
            <div
              style={{
                marginTop: -10,
                width: '100%',
                position: 'relative',
              }}
            >
              <CodeAccordianAdaptor
                diff={tool.diff!}
                code={tool.diff!}
                isFeedback={true}
                isExpanded={isExpanded}
                onToggleExpand={onToggleExpand}
              />
              {tool.diff && (
                <MessageToolbar
                shouldShow={shouldShowToolbar}
                  isGenerating={isMessageGenerating}
                  allMessages={allMessages}
                  messageIndex={messageIndex}
                  currentMessage={message}
                  messageTs={message.ts}
                />
              )}
            </div>
          );
        }
        case 'error':
          return (
            <>
              {title && (
                <div style={headerStyle}>
                  {icon}
                  {title}
                </div>
              )}
              <p
                style={{
                  ...pStyle,
                  color: 'var(--vscode-foreground)',
                }}
                dangerouslySetInnerHTML={{
                  __html: message.text ?? '',
                }}
              ></p>
            </>
          );
        case 'diff_error':
          return (
            <>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  backgroundColor: 'rgba(255, 191, 0, 0)',
                  padding: 8,
                  borderRadius: 3,
                  fontSize: 12,
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    marginBottom: 4,
                  }}
                >
                  <i
                    className="codicon codicon-info"
                    style={{
                      marginRight: 8,
                      fontSize: 18,
                      // color: '#FFA500',
                    }}
                  ></i>
                  <span
                    style={{
                      fontWeight: 500,
                      // color: '#FFA500',
                    }}
                  >
                    差异修改异常，正在重试中...
                  </span>
                </div>
                <div>模型使用的搜索模式与文件中的任何内容都不匹配，重试中...</div>
              </div>
            </>
          );
        case 'joycoderignore_error':
          return (
            <>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  backgroundColor: 'rgba(255, 191, 0, 0.1)',
                  padding: 8,
                  borderRadius: 3,
                  fontSize: 12,
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    marginBottom: 4,
                  }}
                >
                  <i
                    className="codicon codicon-warning"
                    style={{
                      marginRight: 8,
                      fontSize: 18,
                      color: '#FFA500',
                    }}
                  ></i>
                  <span
                    style={{
                      fontWeight: 500,
                      color: '#FFA500',
                    }}
                  >
                    拒绝访问
                  </span>
                </div>
                <div>
                  JoyCode 尝试访问 <code>{message.text}</code>，但它被 <code>.joycodeignore</code> 文件所阻止。
                </div>
              </div>
            </>
          );
        case 'checkpoint_created':
          // 只有在shouldShowToolbar为true时才渲染MessageToolbar
          if (shouldShowToolbar) {
            return (
              <>
                <MessageToolbar
                  shouldShow={shouldShowToolbar}
                  isGenerating={isMessageGenerating}
                  allMessages={allMessages}
                  messageIndex={messageIndex}
                  currentMessage={message}
                  messageTs={message.ts}
                />
              </>
            );
          }
          return null;
        case 'no_workspace_prompt':
          // 显示AI风格的NoWorkspacePrompt组件
          return (
            <>
              {title && (
                <div style={headerStyle}>
                  {icon}
                  {title}
                </div>
              )}
              <div style={{ paddingTop: 10, position: 'relative' }} onCopy={handleCopy}>
                <NoWorkspacePrompt />
              </div>
            </>
          );
        case 'get_mcp_instructions':
          return (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                color: 'var(--vscode-foreground)',
                opacity: 0.7,
                fontSize: 12,
                padding: '4px 0',
              }}
            >
              <i className="codicon codicon-book" style={{ marginRight: 6 }} />
              正在加载 MCP 内容...
            </div>
          );
        case 'completion_result':
          return (
            <>
              {/* 不渲染标题头部，因为图标和标题都被设置为隐藏 */}
              <div
                style={{
                  color: 'var(--vscode-foreground)',
                  // color: 'var(--vscode-charts-green)',
                  paddingTop: 10,
                  position: 'relative',
                }}
                onCopy={handleCopy}
              >
                <Markdown markdown={messageText?.text} />
                {messageText?.text && (
                  <MessageToolbar
                shouldShow={shouldShowToolbar}
                    isGenerating={isMessageGenerating}
                    allMessages={allMessages}
                    messageIndex={messageIndex}
                    currentMessage={message}
                    messageTs={message.ts}
                  />
                )}
              </div>
              {message.partial !== true && hasChanges && (
                <div style={{ paddingTop: 17 }}>
                  <SuccessButton
                    disabled={seeNewChangesDisabled}
                    onClick={() => {
                      setSeeNewChangesDisabled(true);
                      vscode.postMessage({
                        type: 'taskCompletionViewChanges',
                        number: message.ts,
                      });
                    }}
                    style={{
                      width: '100%',
                      cursor: seeNewChangesDisabled ? 'wait' : 'pointer',
                      background: 'var(--vscode-button-secondaryBackground, #72747C)',
                      color: 'var(--vscode-button-secondaryForeground, #72747C)',
                      borderRadius: '4px',
                      height: '28px',
                    }}
                  >
                    <i className="icon iconfont icon-rizhi1" style={{ marginRight: 6 }} />
                    查看最新变更
                  </SuccessButton>
                </div>
              )}
            </>
          );
        case 'shell_integration_warning':
          return (
            <>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  backgroundColor: 'rgba(255, 191, 0, 0)',
                  padding: 8,
                  borderRadius: 3,
                  fontSize: 12,
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    marginBottom: 4,
                  }}
                >
                  <i
                    className="codicon codicon-warning"
                    style={{
                      marginRight: 8,
                      fontSize: 18,
                      color: '#FFA500',
                    }}
                  ></i>
                  <span
                    style={{
                      fontWeight: 500,
                      // color: '#FFA500',
                    }}
                  >
                    Shell 集成不可用
                  </span>
                </div>
                <div>
                  JoyCode 将无法查看命令的输出。请更新 VSCode（
                  <code>CMD/CTRL + Shift + P</code> → "更新"）并确保您使用的是受支持的 shell：zsh、bash、 fish 或
                  PowerShell（<code>CMD/CTRL + Shift + P</code> → "终端：选择默认配置文件"）。{' '}
                  <a
                    href="http://joycoder.jd.com/jdhopenplatform/JoyCoder/docs/prompts/shell#%E9%92%88%E5%AF%B9-windows-%E7%94%A8%E6%88%B7%E7%9A%84%E5%85%B6%E4%BB%96%E6%95%85%E9%9A%9C%E6%8E%92%E9%99%A4"
                    style={{
                      color: 'inherit',
                      textDecoration: 'underline',
                    }}
                  >
                    仍然遇到问题？
                  </a>
                </div>
              </div>
            </>
          );
        case 'mcp_server_response':
          return (
            <>
              <div style={{ paddingTop: 0 }}>
                <div
                  style={{
                    marginBottom: '4px',
                    opacity: 0.8,
                    fontSize: '12px',
                    textTransform: 'uppercase',
                  }}
                >
                  响应
                </div>
                <CodeAccordianAdaptor
                  code={message.text}
                  language="json"
                  isExpanded={true}
                  onToggleExpand={onToggleExpand}
                />
              </div>
            </>
          );
        // 调用Codebase搜索，但无法生效的提示
        case 'use_codebase_not_support': {
          return (
            <div>
              无法通过Codebase进行搜索，原因：
              {message.text === 'UNINDEXED' ? (
                <div>
                  未开启Codebase索引，
                  <button
                    className="joycoder-link-button" // 添加CSS使其看起来像链接
                    onClick={(e) => {
                      vscode.postMessage({
                        type: 'openSettings',
                      });
                    }}
                  >
                    去开启
                  </button>
                </div>
              ) : message.text === 'INDEXING' ? (
                <div>
                  索引进行中，
                  <button
                    className="joycoder-link-button" // 添加CSS使其看起来像链接
                    onClick={(e) => {
                      vscode.postMessage({
                        type: 'openSettings',
                      });
                    }}
                  >
                    查看进展
                  </button>
                </div>
              ) : (
                '未知原因' + message.text
              )}
            </div>
          );
        }
        case 'deleted_api_reqs':
          // deleted_api_reqs消息不显示任何内容
          return null;
        case 'api_req_retried':
          // api_req_retried消息不显示任何内容
          return null;
        case 'codebase_search_result':
          let parsed: {
            content: {
              query: string;
              results: Array<{
                filePath: string;
                score: number;
                startLine: number;
                endLine: number;
                codeChunk: string;
              }>;
            };
          } | null = null;

          try {
            if (message.text) {
              parsed = JSON.parse(message.text);
            }
          } catch (error) {
            console.error('Failed to parse codebaseSearch content:', error);
          }

          if (parsed && !parsed?.content) {
            console.error('Invalid codebaseSearch content structure:', parsed.content);
            return <div>显示搜索结果时出错。</div>;
          }

          const { query = '', results = [] } = parsed?.content || {};

          return <CodebaseSearchResultsDisplay query={query} results={results} />;
        default:
          return (
            <>
              {title && (
                <div style={headerStyle}>
                  {icon}
                  {title}
                </div>
              )}
              <div style={{ paddingTop: 10, position: 'relative' }} onCopy={handleCopy}>
                <Markdown markdown={message.text} />
                {message.text && (
                  <MessageToolbar
                shouldShow={shouldShowToolbar}
                    isGenerating={isMessageGenerating}
                    allMessages={allMessages}
                    messageIndex={messageIndex}
                    currentMessage={message}
                    messageTs={message.ts}
                  />
                )}
              </div>
            </>
          );
      }
    case 'ask':
      switch (message.ask) {
        case 'mistake_limit_reached':
          return (
            <>
              <div style={headerStyle}>
                {icon}
                {title}
              </div>
              <p
                style={{
                  ...pStyle,
                  color: 'var(--vscode-warnForeground)',
                }}
              >
                {message.text}
              </p>
            </>
          );
        //@ts-ignore
        case 'command':
          return (
            <>
              <div style={headerStyle}>
                {icon}
                {title}
              </div>
              <CommandExecution executionId={message.ts.toString()} text={message.text} />
              {/* 为命令消息添加工具条 */}
              {message.text && (
                <div style={{ position: 'relative' }}>
                  <MessageToolbar
                shouldShow={shouldShowToolbar}
                    isGenerating={isMessageGenerating}
                    allMessages={allMessages}
                    messageIndex={messageIndex}
                    currentMessage={message}
                    messageTs={message.ts}
                  />
                </div>
              )}
            </>
          );
        case 'auto_approval_max_req_reached':
          return (
            <>
              <div style={headerStyle}>
                {icon}
                {title}
              </div>
              <p
                style={{
                  ...pStyle,
                  color: 'var(--vscode-foreground)',
                }}
              >
                {message.text}
              </p>
            </>
          );
        case 'new_task_with_condense_context':
          return (
            <>
              <div style={headerStyle}>
                <span
                  className="codicon codicon-new-file"
                  style={{
                    color: normalColor,
                    marginBottom: '-1.5px',
                  }}
                ></span>
                <span style={{ color: normalColor, fontWeight: 'bold' }}>JoyCode 想要创建带当前上下文的新任务：</span>
              </div>
              <NewTaskPreview context={message.text || ''} />
            </>
          );
        case 'completion_result':
          if (message.text) {
            // 修复：这个是否真的被使用过？
            const hasChanges = message.text.endsWith(COMPLETION_RESULT_CHANGES_FLAG) ?? false;
            const text = hasChanges ? message.text.slice(0, -COMPLETION_RESULT_CHANGES_FLAG.length) : message.text;
            return (
              <div>
                {/* 不渲染标题头部，因为图标和标题都被设置为隐藏 */}
                <div
                  style={{
                    color: 'var(--vscode-foreground)',
                    // color: 'var(--vscode-charts-green)',
                    paddingTop: 10,
                    position: 'relative',
                  }}
                >
                  <Markdown markdown={text} />
                  {text && (
                    <MessageToolbar
                shouldShow={shouldShowToolbar}
                      isGenerating={isMessageGenerating}
                      allMessages={allMessages}
                      messageIndex={messageIndex}
                      currentMessage={message}
                      messageTs={message.ts}
                    />
                  )}
                  {message.partial !== true && hasChanges && (
                    <div style={{ marginTop: 15 }}>
                      <SuccessButton
                        appearance="secondary"
                        disabled={seeNewChangesDisabled}
                        onClick={() => {
                          setSeeNewChangesDisabled(true);
                          vscode.postMessage({
                            type: 'taskCompletionViewChanges',
                            number: message.ts,
                          });
                        }}
                        style={{
                          width: '100%',
                          cursor: seeNewChangesDisabled ? 'wait' : 'pointer',
                          background: 'var(--vscode-button-secondaryBackground, #72747C)',
                          color: 'var(--vscode-button-secondaryForeground, #72747C)',
                          borderRadius: '4px',
                          height: '28px',
                        }}
                      >
                        <i className="icon iconfont icon-rizhi1" style={{ marginRight: 6 }} />
                        查看最新变更
                      </SuccessButton>
                    </div>
                  )}
                </div>
              </div>
            );
          } else {
            return null; // 当我们得到没有文本的completion_result询问时，不渲染任何内容
          }
        case 'followup': {
          let question: string | undefined;
          let options: string[] | undefined;
          let selected: string | undefined;
          try {
            const parsedMessage = JSON.parse(message.text || '{}') as JoyCoderAskQuestion;
            question = parsedMessage.question;
            options = parsedMessage.options;
            selected = parsedMessage.selected;
          } catch (e) {
            // 旧版消息会直接传递问题
            question = message.text;
          }

          return (
            <>
              {title && (
                <div style={headerStyle}>
                  {icon}
                  {title}
                </div>
              )}
              <div style={{ paddingTop: 10, position: 'relative' }}>
                <Markdown markdown={question} />
                {question && (
                  <MessageToolbar
                shouldShow={shouldShowToolbar}
                    isGenerating={isMessageGenerating}
                    allMessages={allMessages}
                    messageIndex={messageIndex}
                    currentMessage={message}
                    messageTs={message.ts}
                  />
                )}
                <OptionsButtons
                  options={options}
                  selected={selected}
                  isActive={isLast && lastModifiedMessage?.ask === 'followup'}
                />
              </div>
              {followUpData && followUpData.suggest?.length > 0 && (
                <>
                  <div style={{ paddingTop: 10, paddingBottom: 15 }}>
                    <Markdown markdown={message.partial === true ? message?.text : followUpData?.question} />
                  </div>
                  <FollowUpSuggest suggestions={followUpData?.suggest} ts={message?.ts} />
                </>
              )}
            </>
          );
        }
        case 'condense':
          return (
            <>
              <div style={headerStyle}>
                <span
                  className="codicon codicon-new-file"
                  style={{
                    color: normalColor,
                    marginBottom: '-1.5px',
                  }}
                ></span>
                <span style={{ color: normalColor, fontWeight: 'bold' }}>JoyCode将要简化并提取当前上下文：</span>
              </div>
              <NewTaskPreview context={message.text || ''} />
            </>
          );
        case 'get_plan_info':
          let response: string | undefined;
          let options: string[] | undefined;
          let selected: string | undefined;
          try {
            const parsedMessage = JSON.parse(message.text || '{}') as JoyCoderPlanModeResponse;
            response = parsedMessage.response;
            options = parsedMessage.options;
            selected = parsedMessage.selected;
          } catch (e) {
            // 旧版消息会直接传递响应
            response = message.text;
          }
          return (
            <>
              <div style={{ position: 'relative' }}>
                <Markdown markdown={response} />
                {response && (
                  <MessageToolbar
                shouldShow={shouldShowToolbar}
                    isGenerating={isMessageGenerating}
                    allMessages={allMessages}
                    messageIndex={messageIndex}
                    currentMessage={message}
                    messageTs={message.ts}
                  />
                )}
                <OptionsButtons
                  options={options}
                  selected={selected}
                  isActive={isLast && lastModifiedMessage?.ask === 'get_plan_info'}
                />
              </div>
            </>
          );
        // return (
        //   <div style={{}}>
        //     <Markdown markdown={message.text} />
        //   </div>
        // );
        default:
          return null;
      }
  }
};

export const ProgressIndicator = () => (
  <div
    style={{
      width: '16px',
      height: '16px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}
  >
    <div style={{ transform: 'scale(0.55)', transformOrigin: 'center' }}>
      <VSCodeProgressRing />
    </div>
  </div>
);

const Markdown = memo(({ markdown }: { markdown?: string }) => {
  return (
    <div
      style={{
        wordBreak: 'break-word',
        overflowWrap: 'anywhere',
        marginBottom: -3,
        marginTop: -10,
      }}
    >
      <MarkdownBlock markdown={markdown} />
    </div>
  );
});
