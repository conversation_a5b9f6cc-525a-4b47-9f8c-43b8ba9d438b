import React, { memo, useEffect, useCallback } from 'react';
import { useRemark } from 'react-remark';
import rehypeHighlight, { Options } from 'rehype-highlight';
import styled from 'styled-components';
import { visit } from 'unist-util-visit';
import { useExtensionState } from '../../context/ExtensionStateContext';
import { CODE_BLOCK_BG_COLOR } from './CodeBlock';
import MermaidBlock from './MermaidBlock';
import { vscode } from '../../utils/vscode';
import { safeDecodeURI } from '../../utils/format';
// import mermaid from 'mermaid';
interface MarkdownBlockProps {
  markdown?: string;
}

/**
 * 文件扩展名配置
 */
const FILE_EXTENSIONS = new Set([
  // 代码文件
  '.js', '.jsx', '.ts', '.tsx', '.vue', '.py', '.java', '.cpp', '.c', '.h', '.hpp',
  '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala', '.clj', '.hs',
  '.ml', '.fs', '.vb', '.pl', '.sh', '.bat', '.ps1',
  // 配置文件
  '.json', '.xml', '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf',
  // 文档文件
  '.md', '.txt', '.rst', '.adoc',
  // 样式文件
  '.css', '.scss', '.sass', '.less', '.styl',
  // 其他
  '.html', '.htm', '.sql', '.r', '.m', '.dockerfile', '.gitignore', '.env'
]);

/**
 * 非文件路径的协议前缀
 */
const NON_FILE_PROTOCOLS = ['http://', 'https://', 'mailto:', 'javascript:', 'ftp:', 'tel:'];

/**
 * 检测链接是否为文件路径
 * @param href 链接地址（原始或已解码）
 * @returns 是否为文件路径
 */
const isFilePath = (href: string): boolean => {
  if (!href?.trim()) return false;

  const normalizedHref = href.trim().toLowerCase();

  // 排除非文件协议
  if (NON_FILE_PROTOCOLS.some(protocol => normalizedHref.startsWith(protocol))) {
    return false;
  }

  // 排除锚点链接
  if (normalizedHref.startsWith('#')) {
    return false;
  }

  // 检查是否包含路径分隔符（表示是路径结构）
  const hasPathStructure = /[/\\]/.test(href);

  // 检查是否有已知的文件扩展名
  const hasKnownExtension = FILE_EXTENSIONS.has(
    href.toLowerCase().match(/\.[^./\\]*$/)?.[0] || ''
  );

  return hasPathStructure || hasKnownExtension;
};

/**
 * Custom remark plugin that converts plain URLs in text into clickable links
 *
 * The original bug: We were converting text nodes into paragraph nodes,
 * which broke the markdown structure because text nodes should remain as text nodes
 * within their parent elements (like paragraphs, list items, etc.).
 * This caused the entire content to disappear because the structure became invalid.
 */
const remarkUrlToLink = () => {
  return (tree: any) => {
    // Visit all "text" nodes in the markdown AST (Abstract Syntax Tree)
    visit(tree, 'text', (node: any, index, parent) => {
      const urlRegex = /(https?):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/g;
      const children: any[] = [];
      const matches = [...node.value.matchAll(urlRegex)];
      if (!matches) return;
      let lastIndex = 0;

      for (const match of matches) {
        // 添加匹配前的文本
        if (match.index > lastIndex) {
          children.push({ type: 'text', value: node.value.substring(lastIndex, match.index) });
        }

        // 添加匹配的文本
        children.push({ type: 'link', url: match[0], children: [{ type: 'text', value: match[0] }] });

        lastIndex = match.index + match[0].length;
      }

      // 添加最后一个匹配后的剩余文本
      if (lastIndex < node.value.length) {
        children.push({ type: 'text', value: node.value.substring(lastIndex) });
      }

      // Fix: Instead of converting the node to a paragraph (which broke things),
      // we replace the original text node with our new nodes in the parent's children array.
      // This preserves the document structure while adding our links.
      if (parent) {
        parent.children.splice(index, 1, ...children);
      }
    });
  };
};

/**
 * Custom remark plugin that prevents filenames with extensions from being parsed as bold text
 * For example: __init__.py should not be rendered as bold "init" followed by ".py"
 * Solves https://github.com/joycoder/joycoder/issues/1028
 */
const remarkPreventBoldFilenames = () => {
  return (tree: any) => {
    visit(tree, 'strong', (node: any, index: number | undefined, parent: any) => {
      // Only process if there's a next node (potential file extension)
      if (!parent || typeof index === 'undefined' || index === parent.children.length - 1) return;

      const nextNode = parent.children[index + 1];

      // Check if next node is text and starts with . followed by extension
      if (nextNode.type !== 'text' || !nextNode.value.match(/^\.[a-zA-Z0-9]+/)) return;

      // If the strong node has multiple children, something weird is happening
      if (node.children?.length !== 1) return;

      // Get the text content from inside the strong node
      const strongContent = node.children?.[0]?.value;
      if (!strongContent || typeof strongContent !== 'string') return;

      // Validate that the strong content is a valid filename
      if (!strongContent.match(/^[a-zA-Z0-9_-]+$/)) return;

      // Combine into a single text node
      const newNode = {
        type: 'text',
        value: `__${strongContent}__${nextNode.value}`,
      };

      // Replace both nodes with the combined text node
      parent.children.splice(index, 2, newNode);
    });
  };
};

const StyledMarkdown = styled.div`
  pre {
    background-color: ${CODE_BLOCK_BG_COLOR};
    border-radius: 3px;
    margin: 13x 0;
    padding: 10px 10px;
    max-width: calc(100vw - 20px);
    overflow-x: auto;
    overflow-y: hidden;
  }

  pre > code {
    .hljs-deletion {
      background-color: var(--vscode-diffEditor-removedTextBackground);
      display: inline-block;
      width: 100%;
    }
    .hljs-addition {
      background-color: var(--vscode-diffEditor-insertedTextBackground);
      display: inline-block;
      width: 100%;
    }
  }

  code {
    span.line:empty {
      display: none;
    }
    word-wrap: break-word;
    border-radius: 3px;
    background-color: ${CODE_BLOCK_BG_COLOR};
    font-size: var(--vscode-editor-font-size, var(--vscode-font-size, 12px));
    font-family: var(--vscode-editor-font-family);
  }

  a > code:not(pre > code) {
    font-family: var(--vscode-editor-font-family, monospace);
    color: var(--vscode-textPreformat-foreground, #f78383);
    background-color: var(--vscode-textCodeBlock-background, #1e1e1e);
    padding: 0px 2px;
    border-radius: 3px;
    border: 1px solid var(--vscode-textSeparator-foreground, #424242);
    white-space: pre-line;
    word-break: break-word;
    overflow-wrap: anywhere;
  }

  p > code:not(pre > code) {
    font-family: var(--vscode-editor-font-family, monospace);
    color: var(--vscode-editor-foreground, #f78383);
    background-color: var(--vscode-editor-background, #1e1e1e);
    padding: 0px 2px;
    border-radius: 3px;
    border: 1px solid var(--vscode-editor-foreground, #424242);
    white-space: pre-line;
    word-break: break-word;
    overflow-wrap: anywhere;
  }

  font-family:
    var(--vscode-font-family),
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Open Sans',
    'Helvetica Neue',
    sans-serif;
  font-size: var(--vscode-font-size, 13px);

  p,
  li,
  ol,
  ul {
    line-height: 22px;
    margin: 0;
    font-size: 13px;
    font-family: PingFang SC;
    font-weight: normal;
    color: var(--vscode-editor-foreground);
  }
  
  h1, h2, h3, h4, h5, h6 {
    color: var(--vscode-editor-foreground) !important;
  }

  ol,
  ul {
    padding-left: 2.5em;
    margin-left: 0;
  }

  p {
    white-space: pre-wrap;
  }

  a {
    text-decoration: none;
  }
  a {
    &:hover {
      text-decoration: underline;
    }
  }
`;

const StyledPre = styled.pre<{ theme: any }>`
  & .hljs {
    color: var(--vscode-editor-foreground, #fff);
  }

  ${(props) =>
    Object.keys(props.theme)
      .map((key) => {
        return `
      & ${key} {
        color: ${props.theme[key]};
      }
    `;
      })
      .join('')}
`;

const MarkdownBlock = memo(({ markdown }: MarkdownBlockProps) => {
  const { theme } = useExtensionState();

  // 处理文件链接点击事件
  const handleFileLinkClick = useCallback((event: React.MouseEvent, href: string) => {
    event.preventDefault();

    // 解码URL编码的字符（如中文字符）
    let filePath = safeDecodeURI(href);

    // 如果路径以 / 开头，移除开头的 /，因为这通常是相对路径
    if (filePath.startsWith('/')) {
      filePath = filePath.substring(1);
    }

    // 调试信息（可在需要时启用）
    // console.log('MarkdownBlock: 原始href:', href);
    // console.log('MarkdownBlock: 解码后的filePath:', filePath);

    // 发送消息到VSCode打开文件
    vscode.postMessage({
      type: 'openFile',
      text: filePath,
    });
  }, []);
  // const ref = useRef<HTMLDivElement>(null);
  // const refText = ref?.current?.innerText;
  // useEffect(() => {
  //   new Promise((resolve) => setTimeout(resolve, 1000)).then(() => {
  //     try {
  //       if (ref?.current) {
  //         mermaid.initialize({ startOnLoad: true });
  //         // mermaid.run({
  //         //   nodes: [ref?.current],
  //         //   suppressErrors: true,
  //         // });
  //         //   .catch((e: any) => {
  //         //     console.error('[Mermaid] ', e.message);
  //         //   });
  //       }
  //     } catch (error) {
  //       console.error('%c [ error ]-261', 'font-size:13px; background:pink; color:#bf2c9f;', error);
  //     }
  //   });
  // }, [refText]);
  const [reactContent, setMarkdown] = useRemark({
    remarkPlugins: [
      remarkPreventBoldFilenames,
      remarkUrlToLink,
      () => {
        return (tree) => {
          visit(tree, 'code', (node: any) => {
            if (!node.lang) {
              node.lang = 'javascript';
            } else if (node.lang.includes('.')) {
              node.lang = node.lang.split('.').slice(-1)[0];
            }
          });
        };
      },
    ],
    rehypePlugins: [
      rehypeHighlight as any,
      {
        // languages: {},
      } as Options,
    ],
    rehypeReactOptions: {
      components: {
        // 自定义链接组件，处理文件路径链接
        a: ({ href, children, ...props }: any) => {
          // 先解码href，然后检查是否为文件路径
          const decodedHref = safeDecodeURI(href);

          if (href && isFilePath(decodedHref)) {
            return (
              <a
                {...props}
                href="#"
                onClick={(event) => handleFileLinkClick(event, href)}
                style={{
                  color: 'var(--vscode-textLink-foreground)',
                  textDecoration: 'none',
                  cursor: 'pointer',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.textDecoration = 'underline';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.textDecoration = 'none';
                }}
                title={`点击打开文件: ${decodedHref}`}
              >
                {children}
              </a>
            );
          }

          // 对于非文件路径链接，使用默认行为
          return (
            <a {...props} href={href}>
              {children}
            </a>
          );
        },
        // pre: ({ node, ...preProps }: any) => <StyledPre {...preProps} theme={theme} />,
        pre: ({ node, children, ...preProps }: any) => {
          if (Array.isArray(children) && children.length === 1 && React.isValidElement(children[0])) {
            const child = children[0] as React.ReactElement<{ className?: string }>;
            if (child.props?.className?.includes('language-mermaid')) {
              return child;
            }
          }
          return (
            <StyledPre {...preProps} theme={theme}>
              {children}
            </StyledPre>
          );
        },
        //   code: (props: any) => {
        //     const className = props.className || '';
        //     if (className.includes('language-mermaid')) {
        //       return (
        //         <>
        //           {props?.children && (
        //             <div className="mermaid" ref={ref}>
        //               {props?.children}
        //             </div>
        //           )}
        //         </>
        //       );
        //     }
        //     return <code {...props} />;
        //   },
        code: (props: any) => {
          const className = props.className || '';
          if (className.includes('language-mermaid')) {
            const codeText = String(props.children || '');
            return <MermaidBlock code={codeText} />;
          }
          return <code {...props} />;
        },
      },
    },
  });

  useEffect(() => {
    setMarkdown(markdown || '');
  }, [markdown, setMarkdown, theme]);

  return (
    <div style={{}}>
      <StyledMarkdown>{reactContent}</StyledMarkdown>
    </div>
  );
});

export default MarkdownBlock;
