import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import './CustomTooltip.css';

interface CustomTooltipProps {
  color?: string;
  title?: string;
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight';
  hidden?: boolean;
  children: React.ReactElement;
  scale?: number;
}

// 获取箭头样式的辅助函数
const getArrowStyle = (placement: string, color: string) => {
  const arrowSize = 6;

  switch (placement) {
    case 'top':
      return {
        bottom: -arrowSize,
        left: '50%',
        transform: 'translateX(-50%)',
        borderWidth: `${arrowSize}px ${arrowSize}px 0 ${arrowSize}px`,
        borderColor: `${color} transparent transparent transparent`,
      };
    case 'topLeft':
      return {
        bottom: -arrowSize,
        left: '16px',
        borderWidth: `${arrowSize}px ${arrowSize}px 0 ${arrowSize}px`,
        borderColor: `${color} transparent transparent transparent`,
      };
    case 'topRight':
      return {
        bottom: -arrowSize,
        right: '16px',
        borderWidth: `${arrowSize}px ${arrowSize}px 0 ${arrowSize}px`,
        borderColor: `${color} transparent transparent transparent`,
      };
    case 'bottom':
      return {
        top: -arrowSize,
        left: '50%',
        transform: 'translateX(-50%)',
        borderWidth: `0 ${arrowSize}px ${arrowSize}px ${arrowSize}px`,
        borderColor: `transparent transparent ${color} transparent`,
      };
    case 'bottomLeft':
      return {
        top: -arrowSize,
        left: '16px',
        borderWidth: `0 ${arrowSize}px ${arrowSize}px ${arrowSize}px`,
        borderColor: `transparent transparent ${color} transparent`,
      };
    case 'bottomRight':
      return {
        top: -arrowSize,
        right: '16px',
        borderWidth: `0 ${arrowSize}px ${arrowSize}px ${arrowSize}px`,
        borderColor: `transparent transparent ${color} transparent`,
      };
    case 'left':
      return {
        right: -arrowSize,
        top: '50%',
        transform: 'translateY(-50%)',
        borderWidth: `${arrowSize}px 0 ${arrowSize}px ${arrowSize}px`,
        borderColor: `transparent transparent transparent ${color}`,
      };
    case 'right':
      return {
        left: -arrowSize,
        top: '50%',
        transform: 'translateY(-50%)',
        borderWidth: `${arrowSize}px ${arrowSize}px ${arrowSize}px 0`,
        borderColor: `transparent ${color} transparent transparent`,
      };
    default:
      return {
        bottom: -arrowSize,
        left: '50%',
        transform: 'translateX(-50%)',
        borderWidth: `${arrowSize}px ${arrowSize}px 0 ${arrowSize}px`,
        borderColor: `${color} transparent transparent transparent`,
      };
  }
};

const CustomTooltip: React.FC<CustomTooltipProps> = ({
  color = 'rgb(34, 35, 37)',
  title,
  placement = 'top',
  hidden = false,
  children,
  scale = 1,
}) => {
  const [visible, setVisible] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const triggerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  const showTooltip = () => {
    if (!title || hidden) return;
    setVisible(true);
  };

  const hideTooltip = () => {
    setVisible(false);
  };

  const updatePosition = () => {
    if (!triggerRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();

    // 先设置一个临时位置，让tooltip渲染出来以获取尺寸
    let top = triggerRect.top;
    let left = triggerRect.left;

    // 如果tooltip已经渲染，使用实际尺寸计算位置
    if (tooltipRef.current) {
      const tooltipRect = tooltipRef.current.getBoundingClientRect();

      switch (placement) {
        case 'top':
          top = triggerRect.top - tooltipRect.height - 15;
          left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2;
          break;
        case 'bottom':
          top = triggerRect.bottom + 1;
          left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2;
          break;
        case 'left':
          top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2;
          left = triggerRect.left - tooltipRect.width - 15;
          break;
        case 'right':
          top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2;
          left = triggerRect.right + 15;
          break;
        case 'topLeft':
          top = triggerRect.top - tooltipRect.height - 8;
          left = triggerRect.left;
          break;
        case 'topRight':
          top = triggerRect.top - tooltipRect.height - 8;
          left = triggerRect.right - tooltipRect.width;
          break;
        case 'bottomLeft':
          top = triggerRect.bottom + 8;
          left = triggerRect.left;
          break;
        case 'bottomRight':
          top = triggerRect.bottom + 8;
          left = triggerRect.right - tooltipRect.width;
          break;
        default:
          top = triggerRect.top - tooltipRect.height - 8;
          left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2;
      }

      // 确保tooltip不会超出视窗边界
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      if (left < 8) left = 8;
      if (left + tooltipRect.width > viewportWidth - 8) {
        left = viewportWidth - tooltipRect.width - 8;
      }
      if (top < 8) top = 8;
      if (top + tooltipRect.height > viewportHeight - 8) {
        top = viewportHeight - tooltipRect.height - 8;
      }
    }

    setPosition({ top, left });
  };

  useEffect(() => {
    if (visible) {
      // 立即更新位置
      updatePosition();
      // 在下一帧再次更新位置，确保tooltip已经渲染完成
      requestAnimationFrame(() => {
        updatePosition();
      });
    }
  }, [visible, placement]);

  // 合并 refs 的辅助函数
  const mergeRefs = (ref1: React.Ref<any>, ref2: React.Ref<any>) => {
    return (element: any) => {
      if (typeof ref1 === 'function') ref1(element);
      else if (ref1) (ref1 as any).current = element;

      if (typeof ref2 === 'function') ref2(element);
      else if (ref2) (ref2 as any).current = element;
    };
  };

  const clonedChild = React.cloneElement(children, {
    ref: mergeRefs(triggerRef, (children as any).ref),
    onMouseEnter: (e: React.MouseEvent) => {
      showTooltip();
      children.props.onMouseEnter?.(e);
    },
    onMouseLeave: (e: React.MouseEvent) => {
      hideTooltip();
      children.props.onMouseLeave?.(e);
    },
  });

  return (
    <>
      {clonedChild}
      {visible &&
        title &&
        createPortal(
          <div
            ref={tooltipRef}
            className="custom-tooltip"
            style={{
              position: 'fixed',
              top: position.top,
              left: position.left,
              backgroundColor: color,
              color: 'var(--vscode-button-secondaryForeground, #72747C)',
              padding: '6px 8px',
              borderRadius: '4px',
              fontSize: '12px',
              zIndex: 9999,
              pointerEvents: 'none',
              maxWidth: '250px',
              whiteSpace: 'normal',
              wordWrap: 'break-word',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
              transform: `scale(${scale})`,
            }}
          >
            {title}
            {/* 箭头 */}
            <div
              style={{
                position: 'absolute',
                width: 0,
                height: 0,
                borderStyle: 'solid',
                ...getArrowStyle(placement, color),
              }}
            />
          </div>,
          document.body,
        )}
    </>
  );
};

export default CustomTooltip;
