{"name": "@joycoder/agent-setting", "private": true, "version": "3.0.0", "type": "module", "scripts": {"start": "vite", "build": "tsc && vite build", "watch": "tsc && vite build --watch", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "antd": "^4.24.14", "lottie-react": "^2.4.1", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "sass": "^1.77.8", "terser": "^5.43.1", "typescript": "^5.2.2", "vite": "^5.2.0"}}