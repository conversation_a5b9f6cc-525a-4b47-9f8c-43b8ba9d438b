import React from 'react';
import { Button, Progress, Popconfirm } from 'antd';
import { CodebaseIndexingStatus } from '@joycoder/shared/src/types/codebase';

interface CodebaseTabProps {
  hasWorkspaceFolder: boolean;
  codebaseIndexingProgress: number;
  codebaseIndexingStatus: CodebaseIndexingStatus;
  codebaseIndexingButtonDisabled: boolean;
  onCodebaseIndexing: (action: string) => () => void;
}

export default function CodebaseTab({
  hasWorkspaceFolder,
  codebaseIndexingProgress,
  codebaseIndexingStatus,
  codebaseIndexingButtonDisabled,
  onCodebaseIndexing,
}: CodebaseTabProps) {
  return (
    <div className="setting-tabs-item">
      <div className="setting-tabs-item-title">上下文</div>
      <div className="joycoder-setting-box">
        <div className="joycoder-setting-box-title">代码库索引</div>
        <div className="joycoder-setting-box-text l-20 mt-10">
          构建代码库索引，可以增强智能体对代码库的理解，提升生成效果。构建索引成功后，智能体将自主获取代码库索引；必要时您也可以通过
          @Codebase 的方式要求智能体使用它。
        </div>
        {hasWorkspaceFolder && (
          <>
            {codebaseIndexingStatus === CodebaseIndexingStatus.INDEXING && (
              <div className="joycoder-loading-dots mt-16">索引中({codebaseIndexingProgress}%)</div>
            )}
            {codebaseIndexingStatus === CodebaseIndexingStatus.UNINDEXED && (
              <div className="joycoder-setting-box-msg mt-16">
                <i className="icon iconfont icon-tanhao mr-4"></i>
                代码库索引未构建
              </div>
            )}
            {codebaseIndexingStatus === CodebaseIndexingStatus.PREPARING && (
              <div className="joycoder-loading-dots mt-16">Codebase服务准备中</div>
            )}
            {codebaseIndexingStatus === CodebaseIndexingStatus.NO_WORKSPACE && (
              <div className="joycoder-setting-box-msg mt-16">
                <i className="icon iconfont icon-tanhao mr-4"></i>
                未检测到工作目录，请打开文件夹后重试
              </div>
            )}
            {codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED && <div className="mt-16">索引完成</div>}
            {(codebaseIndexingStatus === CodebaseIndexingStatus.INDEXING ||
              codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED) && (
              <Progress
                percent={codebaseIndexingProgress}
                type="line"
                status={
                  codebaseIndexingProgress === 100 && codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED
                    ? 'success'
                    : 'active'
                }
                showInfo={false}
              />
            )}

            {codebaseIndexingStatus === CodebaseIndexingStatus.UNINDEXED && (
              <Button
                type="default"
                style={{
                  backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
                  borderColor: 'var(--vscode-input-border, #72747c)',
                  color: 'var(--vscode-input-foreground, #72747c)',
                  width: '72px',
                  height: '28px',
                  marginTop: '12px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  padding: '0',
                  marginRight: '12px',
                }}
                disabled={codebaseIndexingButtonDisabled}
                onClick={onCodebaseIndexing('start')}
              >
                开始索引
              </Button>
            )}
            {(codebaseIndexingStatus === CodebaseIndexingStatus.INDEXING ||
              codebaseIndexingStatus === CodebaseIndexingStatus.PREPARING) && (
              <Button
                type="default"
                style={{
                  backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
                  borderColor: 'var(--vscode-input-border, #72747c)',
                  color: 'var(--vscode-input-foreground, #72747c)',
                  width: '72px',
                  height: '28px',
                  marginTop: '12px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  padding: '0',
                  marginRight: '12px',
                }}
                onClick={onCodebaseIndexing('cancel')}
                disabled={codebaseIndexingButtonDisabled}
              >
                取消索引
              </Button>
            )}

            {/* 重新索引 */}
            {codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED && (
              <Button
                type="default"
                onClick={onCodebaseIndexing('start')}
                style={{
                  backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
                  borderColor: 'var(--vscode-input-border, #72747c)',
                  color: 'var(--vscode-input-foreground, #72747c)',
                  width: '72px',
                  height: '28px',
                  marginTop: '12px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  padding: '0',
                  marginRight: '12px',
                }}
                disabled={codebaseIndexingButtonDisabled}
              >
                重新索引
              </Button>
            )}

            {/* 删除索引 */}
            {codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED && (
              <Popconfirm
                title="确定清除索引吗？"
                onConfirm={onCodebaseIndexing('remove')}
                okText="删除"
                cancelText="取消"
              >
                <Button
                  type="default"
                  style={{
                    backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
                    borderColor: 'var(--vscode-input-border, #72747c)',
                    color: 'var(--vscode-input-foreground, #72747c)',
                    width: '72px',
                    height: '28px',
                    marginTop: '12px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    padding: '0',
                  }}
                >
                  删除索引
                </Button>
              </Popconfirm>
            )}
          </>
        )}
        {!hasWorkspaceFolder && (
          <div className="joycoder-setting-box-msg mt-16">
            <i className="icon iconfont icon-tanhao mr-4"></i>
            未检测到工作目录，请打开文件夹后重试
          </div>
        )}
      </div>
    </div>
  );
}
