import React from 'react';
import { List, Avatar, Tag, Dropdown, Checkbox, Switch } from 'antd';
import { ModesInfo } from '../../typings/modes';
import { ACTION_METADATA } from '@joycoder/agent-driven/web-agent/src/utils/chatContant';
import { CommonMessage } from '../../messages/messageTypes';
import { ModeConfig } from '@joycoder/agent-driven/web-agent/src/utils/modes';
import defaultAvatar from '../../assets/images/logo_bg_blue.svg';

interface AgentListProps {
  modesInfo: ModesInfo;
  autoApprovalSettings: any;
  setAutoApprovalSettings: (settings: any) => void;
  onEditAgent: (agentId: string) => void;
  onDeleteAgent: (agentId: string) => void;
  onToggleAgent: (agentId: string, isActive: boolean) => void;
}

const ChatModeIcon: Record<string, string> = {
  architect: 'guihua',
  chat: 'wenda',
  code: 'bianma',
  orchestrator: 'tia<PERSON><PERSON><PERSON>',
  'design-engineer': 'ui',
  debug: 'ceshi',
  promptsButtonClicked: 'xinzeng',
};

export default function AgentList(props: AgentListProps) {
  const { modesInfo, autoApprovalSettings, setAutoApprovalSettings, onEditAgent, onDeleteAgent, onToggleAgent } = props;

  const onAutoApprovalSettingsChange = (e: any) => {
    const checked = e.target.checked;
    const actionId = e.target.value;
    const newActions = {
      ...autoApprovalSettings.actions,
      [actionId]: checked,
    };
    const currentSettings = {
      ...autoApprovalSettings,
      actions: newActions,
      enabled: autoApprovalSettings.enabled,
    };

    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'autoApprovalSettings',
          autoApprovalSettings: currentSettings,
        },
      },
    });

    setAutoApprovalSettings(currentSettings);
  };

  const agentEditListData = (agentId: string) => {
    return [
      {
        key: '1',
        label: <span onClick={() => onEditAgent(agentId)}>编辑</span>,
      },
      {
        key: '2',
        label: (
          <span style={{ color: '#FF4838FF' }} onClick={() => onDeleteAgent(agentId)}>
            删除
          </span>
        ),
      },
    ];
  };

  return (
    <>
      <List
        className="agent-list mt-12"
        header={<span className="agent-list-header">自定义智能体</span>}
        dataSource={modesInfo?.customModes}
        renderItem={(item) => (
          <List.Item key={item.agentId}>
            <List.Item.Meta
              avatar={<Avatar className="agent-list-avatar" src={item.headImg || defaultAvatar} />}
              title={
                <>
                  {item.name}
                  <Tag style={{ marginLeft: 5, fontSize: 10, lineHeight: '18px', paddingLeft: 5, paddingRight: 5 }}>
                    {item.source === 'global' ? '全局' : '项目'}智能体
                  </Tag>
                </>
              }
              description={item.agentDefinition}
            />
            <div className="agent-list-item-options">
              <Switch
                checked={item.isActive ?? true}
                size="small"
                onChange={(isChecked) => {
                  onToggleAgent(item.agentId, isChecked);
                }}
              />
              <Dropdown placement="bottom" menu={{ items: agentEditListData(item.agentId) }}>
                <div className="icon iconfont icon-gengduo"></div>
              </Dropdown>
            </div>
          </List.Item>
        )}
      />

      <List
        className="agent-list mt-8"
        header={<span className="agent-list-header">内置智能体</span>}
        dataSource={modesInfo?.defaultModes as ModeConfig[]}
        renderItem={(item) => (
          <List.Item key={item.agentId}>
            <List.Item.Meta
              avatar={
                <Avatar
                  style={{ fontSize: '20px', lineHeight: '20px' }}
                  className={`agent-list-avatar icon iconfont icon-${
                    ChatModeIcon[item?.agentId] || 'zidingyizhinengtitouxiang'
                  }`}
                />
              }
              title={item.name}
              description={item.description || ''}
            />
            <div className="agent-list-item-options">
              {/* TODO：暂时不支持开关，待完善 */}
              {/* <Switch
                checked={item.isActive ?? true}
                size="small"
                onChange={(isChecked) => {
                  onToggleAgent(item.agentId, isChecked);
                }}
              /> */}
              {/* TODO：暂时不支持进入设置，待完善 */}
              {/* <div className="icon iconfont icon-youjiantou"></div> */}
            </div>
          </List.Item>
        )}
      />

      <div className="joycoder-setting-box">
        <div className="joycoder-setting-box-title mt-16">自动批准</div>
        {ACTION_METADATA.map((action) => (
          <div className="v-h" key={action.id}>
            <div className="joycoder-setting-box-text">
              <Checkbox
                className="joycoder-setting-box-context"
                checked={autoApprovalSettings.actions[action.id]}
                onChange={onAutoApprovalSettingsChange}
                value={action.id}
              >
                {action.label}
              </Checkbox>
            </div>
            <div className="joycoder-setting-box-tip">注：{action.description}</div>
          </div>
        ))}
      </div>
    </>
  );
}
