import React from 'react';
import Lottie from 'lottie-react';

interface LottieAnimationProps {
  /** 动画数据，可以是JSON对象或导入的JSON文件 */
  animationData: any;
  /** 动画样式 */
  style?: React.CSSProperties;
  /** 动画宽度 */
  width?: string | number;
  /** 动画高度 */
  height?: string | number;
  /** 是否循环播放，默认为true */
  loop?: boolean;
  /** 是否自动播放，默认为true */
  autoplay?: boolean;
  /** 动画播放速度，默认为1 */
  speed?: number;
  /** 动画方向，1为正向，-1为反向，默认为1 */
  direction?: number;
  /** 是否暂停 */
  isPaused?: boolean;
  /** 是否停止 */
  isStopped?: boolean;
  /** 动画完成回调 */
  onComplete?: () => void;
  /** 动画循环完成回调 */
  onLoopComplete?: () => void;
  /** 动画进入帧回调 */
  onEnterFrame?: () => void;
  /** 动画分段完成回调 */
  onSegmentStart?: () => void;
  /** 动画数据加载完成回调 */
  onDataReady?: () => void;
  /** 动画数据加载失败回调 */
  onDataFailed?: () => void;
  /** 动画加载完成回调 */
  onLoadedImages?: () => void;
  /** 动画配置错误回调 */
  onConfigReady?: () => void;
  /** 自定义类名 */
  className?: string;
}

/**
 * 通用Lottie动画组件
 * 支持传入不同的JSON动画文件，提供丰富的配置选项
 */
const LottieAnimation: React.FC<LottieAnimationProps> = ({
  animationData,
  style,
  width,
  height,
  loop = true,
  autoplay = true,
  speed = 1,
  direction = 1,
  isPaused = false,
  isStopped = false,
  onComplete,
  onLoopComplete,
  onEnterFrame,
  onSegmentStart,
  onDataReady,
  onDataFailed,
  onLoadedImages,
  onConfigReady,
  className,
}) => {
  // 合并样式
  const mergedStyle: React.CSSProperties = {
    ...style,
    ...(width && { width }),
    ...(height && { height }),
  };

  return (
    <Lottie
      animationData={animationData}
      style={mergedStyle}
      className={className}
      loop={loop}
      autoplay={autoplay}
    />
  );
};

export default LottieAnimation;
