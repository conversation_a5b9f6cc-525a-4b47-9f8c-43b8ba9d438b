import React, { useState, useEffect, memo } from 'react';
import { Modal, Alert, Spin, Input, Radio } from 'antd';
import './McpModal.scss';
import { McpSettingsSchema } from '@joycoder/shared/src/mcp/McpSettingsSchema';
import { mcpPostMessage } from './McpSendMsgUtils';
import {
  McpDeploymentType,
  McpNodeService,
  McpSendMsgType,
  McpCommonSendMsgType,
} from '@joycoder/shared/src/mcp/McpTypes';
import { useHandleMessage } from '../../hooks/useHandleMessage';
interface McpModalProps {
  isModalVisible?: boolean;
  isManualConfigModalVisible: boolean;
  selectedService: McpNodeService | null;
  onClose: () => void;
  onInstalledSuccess: (installedServers: string[]) => void;
  advancedConfig?: boolean;
}

export const McpModal: React.FC<McpModalProps> = memo(
  ({
    isModalVisible,
    isManualConfigModalVisible,
    selectedService,
    onClose,
    onInstalledSuccess,
    advancedConfig = true,
  }) => {
    const [editedServerParam, setEditedServerParam] = useState('');
    const [isModalConfirmLoading, setIsModalConfirmLoading] = useState<boolean>(false);
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    const [envData, setEnvData] = useState<Record<string, string>>({});
    const [isInstallLoading, setIsInstallLoading] = useState<boolean>(false);
    const [successMessage, setSuccessMessage] = useState<string | null>(null);
    const [scope, setScope] = useState<string>('global');
    const [isCollapseOpen, setIsCollapseOpen] = useState<boolean>(false);

    useHandleMessage(({ type, data }) => {
      if (type === McpCommonSendMsgType.SETTING_MCP && data.type === McpSendMsgType.GET_MCP_INSTALL_PARAM) {
        const installParam = data.mcpInstallParam || {};
        if (installParam.success && installParam.data.code === 0) {
          const param = installParam.data.data;

          const paramStr = JSON.stringify(param, null, 2);
          // console.log('MCP 获取安装参数返回:', param);
          const adjustedServerParam = adjustServerParam(selectedService, paramStr || '{}');
          setEditedServerParam(adjustedServerParam);
          parseEnvData(paramStr);
        } else {
          console.error('MCP 获取安装参数失败:', installParam.data?.msg || installParam.message);
          setEditedServerParam('MCP 获取安装参数失败:');
        }
        setIsInstallLoading(false);
      }
      if (type === McpCommonSendMsgType.SETTING_MCP && data.type === McpSendMsgType.UPDATE_OR_INSERT_MCP_SERVER) {
        if (data.fileContent.error) {
          setErrorMessage(data.fileContent.error);
          setIsModalConfirmLoading(false);
        } else {
          const mcpServers = data.fileContent?.mcpServers;
          if (mcpServers) {
            const serverNames = Object.keys(mcpServers);
            onInstalledSuccess(serverNames);
          }
          setTimeout(() => {
            setSuccessMessage('安装成功');
          }, 200);
          // 延迟2秒后关闭模态框
          setTimeout(() => {
            cleanModelParam();
          }, 500);
          //安装成功后 刷新mcp服务
          mcpPostMessage(McpSendMsgType.REFRESH_MCP_SERVICE);
        }
      }
    });
    useEffect(() => {
      onOpenModal();
    }, [isModalVisible, isManualConfigModalVisible]); // 空依赖数组，确保只在挂载时执行一次

    // 解析 serverParam 中的 env 数据
    const parseEnvData = (serverParam: string) => {
      try {
        const parsedParam = JSON.parse(serverParam);
        if (parsedParam?.mcpServers) {
          const serverName = Object.keys(parsedParam.mcpServers)[0];
          const serverConfig = parsedParam.mcpServers[serverName];
          if (serverConfig?.env && typeof serverConfig.env === 'object') {
            setEnvData(serverConfig.env);
          } else {
            setEnvData({});
          }
        } else if (parsedParam?.env && typeof parsedParam?.env === 'object') {
          setEnvData(parsedParam.env);
        } else {
          setEnvData({});
        }
      } catch (error) {
        console.error('Failed to parse serverParam:', error);
        setEnvData({});
      }
    };

    const onOpenModal = () => {
      setIsCollapseOpen(false); // 重置 Collapse 的状态为关闭
      if (selectedService) {
        if (selectedService.isProject) {
          setScope('project');
        }
        if (selectedService.depType === McpDeploymentType.HOSTED && selectedService.isPluginMcp) {
          setEditedServerParam('');
          mcpPostMessage(McpSendMsgType.GET_MCP_INSTALL_PARAM, {
            serviceId: selectedService.serviceId,
            version: selectedService.version,
          });
        } else {
          const adjustedServerParam = adjustServerParam(selectedService, selectedService.serverParam || '{}');
          setEditedServerParam(adjustedServerParam);
          parseEnvData(adjustedServerParam);
          setIsInstallLoading(false);
        }
      }
      if (isManualConfigModalVisible) {
        setEditedServerParam(
          `
//{
//  "mcpServers": {
//    "fetch": {
//      "command": "uvx",
//      "args": [
//        "mcp-server-fetch"
//      ]
//    }
//  }
//}`
        );
      }
    };

    const handleModalOk = () => {
      setIsModalConfirmLoading(true);
      setErrorMessage(null);

      // console.log(`Installing service ${selectedService?.serviceId} editedServerParam ${editedServerParam}`);

      const updatedServerParam = editedServerParam;
      try {
        if (!updatedServerParam || updatedServerParam === '' || updatedServerParam === '{}') {
          throw new Error('MCP 配置文件格式错误，配置文件不能为空');
        }
        const serverParam = JSON.parse(updatedServerParam);
        if (!serverParam || !serverParam.mcpServers || Object.keys(serverParam.mcpServers).length === 0) {
          throw new Error('MCP 配置文件格式错误，缺少 mcpServers 字段');
        }

        const parsedConfig = McpSettingsSchema.safeParse(serverParam);
        if (!parsedConfig.success) {
          // console.log('MCP 配置文件格式错误:', parsedConfig.error);
          throw new Error('MCP 配置文件格式错误，请检查输入内容格式是否正确。');
        }
        console.log('serverParam', updatedServerParam, 'projectType', scope);
        mcpPostMessage(McpSendMsgType.UPDATE_OR_INSERT_MCP_SERVER, {
          serverParam: updatedServerParam,
          projectType: scope,
        });
        // 成功消息和窗口关闭逻辑已移至 useHandleMessage 钩子中
      } catch (error) {
        setErrorMessage(error instanceof Error ? error.message : '更新配置失败，请检查输入内容格式是否正确。');
        setIsModalConfirmLoading(false);
      }
    };

    const handleModalCancel = () => {
      cleanModelParam();
    };
    const cleanModelParam = () => {
      // 清除安装状态
      setEditedServerParam('');
      setEnvData({});
      setIsInstallLoading(false);
      setIsModalConfirmLoading(false);
      setErrorMessage(null);
      setSuccessMessage(null);
      setScope('global');
      onClose();
    };
    const handleServerParamChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = e.target.value;
      setEditedServerParam(newValue);
      parseEnvData(newValue);
    };

    const handleEnvChange = (key: string, value: string) => {
      setEnvData((prev) => {
        const newEnvData = { ...prev, [key]: value };
        setEditedServerParam((prevParam) => {
          const updatedServerParam = JSON.parse(prevParam);
          const serverName = Object.keys(updatedServerParam.mcpServers)[0];
          const serverConfig = updatedServerParam.mcpServers[serverName];
          serverConfig.env = newEnvData;
          return JSON.stringify(updatedServerParam, null, 2);
        });
        return newEnvData;
      });
    };

    return (
      <Modal
        title={
          isManualConfigModalVisible
            ? '手动配置'
            : `添加 「${selectedService?.displayName ?? selectedService?.name ?? ''}」`
        }
        open={isModalVisible || isManualConfigModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        okText="下一步"
        cancelText="取消"
        className="mcp-modal-view__modal"
        confirmLoading={isModalConfirmLoading}
        width={480}
      >
        <div className="mcp-modal-view__modal-content">
          {isManualConfigModalVisible ? (
            <div className="mcp-modal-view__modal-tips">
              请从 MCP Server 的介绍页面复制配置JSON，并粘贴到输入框中。
              <br />
              推荐使用npx或uvx配置。
              <br />
              请按需调整env和args参数。
              <br />
            </div>
          ) : selectedService?.depType !== McpDeploymentType.HOSTED && selectedService?.repository ? (
            <div className="mcp-modal-view__modal-tips">
              请从 <a href={selectedService?.repository}>介绍页面</a> 获取 JSON 配置信息（推荐使用 npx 或
              uvx），并粘贴到输入框中。 注：可能需要调整 args 和 env 设置
            </div>
          ) : null}
          <AlertMessage errorMessage={errorMessage} successMessage={successMessage} />
          <ModalContent
            isInstallLoading={isInstallLoading}
            isModalConfirmLoading={isModalConfirmLoading}
            envData={envData}
            editedServerParam={editedServerParam}
            handleEnvChange={handleEnvChange}
            handleServerParamChange={handleServerParamChange}
          />
        </div>
        {advancedConfig && (
          <div className="mcp-modal-view__modal-advanced-options">
            <AdvancedOptions scope={scope} setScope={setScope} />
          </div>
        )}
      </Modal>
    );
  }
);

const AlertMessage: React.FC<{ errorMessage: string | null; successMessage: string | null }> = memo(
  ({ errorMessage, successMessage }) => {
    if (!errorMessage && !successMessage) return null;
    return (
      <Alert
        message={errorMessage ? '错误' : '成功'}
        description={errorMessage || successMessage}
        type={errorMessage ? 'error' : 'success'}
        showIcon
        className="mcp-modal-view__modal-alert"
      />
    );
  }
);

const ModalContent: React.FC<{
  isInstallLoading: boolean;
  isModalConfirmLoading: boolean;
  envData: Record<string, string>;
  editedServerParam: string;
  handleEnvChange: (key: string, value: string) => void;
  handleServerParamChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
}> = memo(
  ({
    isInstallLoading,
    isModalConfirmLoading,
    envData,
    editedServerParam,
    handleEnvChange,
    handleServerParamChange,
  }) => {
    if (isInstallLoading || isModalConfirmLoading) {
      return (
        <div className="mcp-modal-view__modal-loading">
          <Spin />
          <p>{isInstallLoading ? '正在获取安装参数...' : '正在处理...'}</p>
        </div>
      );
    }

    return (
      <div>
        <EnvDataSection envData={envData} handleEnvChange={handleEnvChange} />
        <div className="mcp-modal-view__modal-json-container">
          <textarea
            value={editedServerParam}
            onChange={handleServerParamChange}
            className="mcp-modal-view__modal-json-textarea"
          />
        </div>
      </div>
    );
  }
);
const EnvDataSection: React.FC<{
  envData: Record<string, string>;
  handleEnvChange: (key: string, value: string) => void;
}> = memo(({ envData, handleEnvChange }) => {
  if (Object.keys(envData).length === 0) return null;
  return (
    <div className="mcp-modal-view__modal-env">
      <h4>环境变量</h4>
      {Object.entries(envData).map(([key, value]) => (
        <div key={key} className="mcp-modal-view__modal-env-item">
          <label htmlFor={`env-${key}`}>{key}</label>
          <Input id={`env-${key}`} value={value} onChange={(e) => handleEnvChange(key, e.target.value)} />
        </div>
      ))}
    </div>
  );
});

export function adjustServerParam(service: any, serverParam: string): string {
  try {
    if (serverParam === '' || serverParam === '{}') {
      return JSON.stringify(
        {
          mcpServers: {},
        },
        null,
        2
      );
    }

    const newServerName = service && service.name ? service.name : '';
    const parsedParam = JSON.parse(serverParam);
    const serverParamObj = parsedParam.mcpServers ? parsedParam.mcpServers : parsedParam;
    if (serverParamObj) {
      delete serverParamObj.isProject;
    }

    const type = serverParamObj.url || serverParamObj.command;

    if (type && newServerName) {
      return JSON.stringify(
        {
          mcpServers: {
            [newServerName]: serverParamObj,
          },
        },
        null,
        2
      );
    }

    if (!type && newServerName) {
      const outServerName = Object.keys(serverParamObj)[0];
      const server = serverParamObj[outServerName];
      if (server) {
        delete server.isProject;
      }
      return JSON.stringify(
        {
          mcpServers: {
            [newServerName]: serverParamObj[outServerName],
          },
        },
        null,
        2
      );
    }

    if (!newServerName && !type) {
      return JSON.stringify(
        {
          mcpServers: {
            ...serverParamObj,
          },
        },
        null,
        2
      );
    }

    return JSON.stringify(serverParamObj, null, 2);
    // {
    //   "hotnews": {
    //     "disabled": true,
    //     "type": "sse",
    //     "url": "http://joycoder-mcp.jd.com/sse/hotnews-autoboots",
    //     "autoApprove": []
    //   }
    // }
    // {
    //     "disabled": true,
    //     "type": "sse",
    //     "url": "http://joycoder-mcp.jd.com/sse/hotnews-autoboots",
    //     "autoApprove": []
    // }
    // {
    //     "command": "npx"
    //     "args": [
    //      "@jd-joycode/mcp-mysql-ts"
    //     ],
    // }
  } catch (error) {
    console.error('Failed to parse serverParam:', error);
    return serverParam;
  }
}
const AdvancedOptions: React.FC<{ scope: string; setScope: (scope: string) => void }> = ({ scope, setScope }) => {
  return (
    <div className="mcp-modal-view__modal-advanced-options-content">
      <div className="mcp-modal-view__modal-advanced-options-item">
        <span style={{ marginRight: '3px' }}>作用范围 : </span>
        <Radio.Group value={scope} onChange={(e) => setScope(e.target.value)}>
          <Radio value="global">全局可用</Radio>
          <Radio value="project">当前项目</Radio>
        </Radio.Group>
      </div>
    </div>
  );
};

export default McpModal;
