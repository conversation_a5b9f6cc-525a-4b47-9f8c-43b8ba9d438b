

.mcp-modal-view {

  &__modal {
    border-radius: 10px;

    .joycoder-dark-modal-content{
      background-color: var(--vscode-editor-background)!important;
      color: var(--vscode-foreground) !important;
      border-radius: 10px;

      .joycoder-dark-modal-body{
        background-color: var(--vscode-editor-background)!important;
        color: var(--vscode-foreground) !important;

        .joycoder-dark-radio-checked {
          .joycoder-dark-radio-inner {
            border-color: var(--vscode-input-foreground) !important;

            &::after {
              background-color: var(--vscode-input-foreground) !important;
            }
          }
        }
      }
      .joycoder-dark-modal-header{
        background-color: var(--vscode-editor-background) !important;
        color: var(--vscode-foreground) !important;
        border-radius: 10px 10px 0 0;
      }
      .joycoder-dark-modal-footer{
        background-color: var(--vscode-editor-background) !important;
        color: var(--vscode-foreground) !important;
        border-radius: 0 0 10px 10px;
        .joycoder-dark-btn-default,.joycoder-dark-btn-default:hover, .joycoder-dark-btn-default:focus {
          background: var(--vscode-button-secondaryBackground)!important;
          color: var(--vscode-button-secondaryForeground)!important;
        }
        .joycoder-dark-btn-primary, .joycoder-dark-btn-primary:hover, .joycoder-dark-btn-primary:focus{
          background: var(--vscode-dropdown-background, #72747C);
          color: var(--vscode-dropdown-foreground, #72747C);
        }
      }
    }

    .joycoder-light-modal-content{
      background-color: var(--vscode-editor-background)!important;
      color: var(--vscode-foreground) !important;

      .joycoder-light-modal-body{
        background-color: var(--vscode-editor-background)!important;
        color: var(--vscode-foreground) !important;
      }
      .joycoder-light-modal-header{
        background-color: var(--vscode-editor-background) !important;
        color: var(--vscode-foreground) !important;
      }
      .joycoder-light-modal-footer{
        background-color: var(--vscode-editor-background) !important;
        color: var(--vscode-foreground) !important;
        .joycoder-light-btn-default,.joycoder-light-btn-default:hover, .joycoder-light-btn-default:focus {
          background: var(--vscode-button-secondaryBackground)!important;
          color: var(--vscode-button-secondaryForeground)!important;
        }
        .joycoder-light-btn-primary, .joycoder-light-btn-primary:hover, .joycoder-light-btn-primary:focus{
          background: var(--vscode-button-background)!important;
          color: var(--vscode-button-foreground)!important;
        }
      }
    }


    &-content {
     background-color: var(--vscode-input-background) !important;
     color: var(--vscode-input-foreground) !important;
     border-radius: 6px;
    }
    &-tips{
      font-size: 12px;
      color: var(--vscode-descriptionForeground);
      background-color: var(--vscode-editor-background);
      padding-bottom: 10px;
      font-family: PingFang SC;
      a{
        color: rgb(36, 127, 255);
      }
    }

    &-alert {
      margin-bottom: 16px;
    }

    &-loading {
      text-align: center;
      padding: 20px;
    }

    &-env {
      background: var(--vscode-editor-background);
      padding-bottom: 16px;
      h4 {
        color: var(--vscode-foreground);
      }

      // &-item {
      //   // Add styles for env item
      // }
    }

    &-json-container {
      position: relative;
      height: 240px;
      //border-radius: 6px;
      overflow: hidden;
    }

    &-json-textarea {
      width: 100%;
      height: 100%;
      background: transparent;
      border: none;
      color: var(--vscode-input-foreground);
      font-family: monospace;
      padding: 10px;
      outline: 0px solid var(--vscode-input-foreground) !important;
      outline-offset: -1px !important;
      border-radius: 6px;
      resize : none
    }

    &-advanced-options {
      margin-top: 16px;

      &-item {
        font-size: 12px;
        display: flex;
        align-items: center;
        color: var(--vscode-input-foreground);

        .joycoder-dark-radio-wrapper{
          font-size: 12px !important;
          color: var(--vscode-input-foreground);
          align-items: center;

          .joycoder-dark-radio {
            top: 0!important;

            &-inner {
              width: 12px;
              height: 12px;
            }
            &-inner::after {
              width: 10px;
              height: 10px;
              margin-top: -5px;
              margin-left: -5px;
            }
          }
        }
      }
    }
  }
}
