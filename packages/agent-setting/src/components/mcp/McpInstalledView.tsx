import { Spin, Switch } from 'antd';
import React, { useState, useEffect, useImperativeHandle } from 'react';
import { useHandleMessage } from '../../hooks/useHandleMessage';
import { mcpPostMessage } from './McpSendMsgUtils';
import { McpSendMsgType, McpCommonSendMsgType } from '@joycoder/shared/src/mcp/McpTypes';
import './McpInstalledView.scss';
import { McpServer } from '@joycoder/shared/src/mcp/mcp';
import TabButton from '../common/TabButton';
import LottieAnimation from '../common/LottieAnimation';
import waitingAnimation from '../../assets/animations/waiting.json';
import { McpModal } from './McpModal';
import { McpNodeService } from '@joycoder/shared/src/mcp/McpTypes';

type McpInstalledViewProps = {
  openDefaultServer?: string;
  onOpenDefaultServerUsed?: () => void;
};

const McpInstalledView = React.forwardRef<{ fetchMcpConnectionServer: () => void }, McpInstalledViewProps>(
  ({ openDefaultServer, onOpenDefaultServerUsed }, ref) => {
    const [isLoading, setIsLoading] = useState<boolean>(false);
    // MCP 服务数据
    const [mcpServers, setMcpServers] = useState<McpServer[]>([]);

    // UI 状态管理
    const [expandedServers, setExpandedServers] = useState<Set<string>>(new Set());
    const [activeTab, setActiveTab] = useState<Record<string, 'tools' | 'resources'>>({});

    // 编辑弹窗状态
    const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
    const [selectedServer, setSelectedServer] = useState<McpNodeService | null>(null);

    useHandleMessage(({ type, data }) => {
      if (type === McpCommonSendMsgType.SETTING_MCP && data.type === McpSendMsgType.GET_MCP_CONNECTION_SERVER) {
        const servers = data.mcpServers || [];
        try {
          setMcpServers(servers);
          console.log('获取 CONNECTION MCP 服务列表成功:', servers);
        } catch (error) {
          console.error('获取 CONNECTION MCP 服务列表失败:', error);
        }
        if (openDefaultServer) {
          defaultOpenServer(openDefaultServer);
        }
        cleanLoading(500);
      }
    });

    const fetchMcpConnectionServer = () => {
      setIsLoading(true);
      mcpPostMessage(McpSendMsgType.GET_MCP_CONNECTION_SERVER);
      cleanLoading(500);
    };

    useEffect(() => {
      fetchMcpConnectionServer();
    }, []); // 空依赖数组，确保只在挂载时执行一次

    useEffect(() => {
      console.log('openDefaultServer', openDefaultServer);
      if (openDefaultServer && mcpServers.length > 0) {
        defaultOpenServer(openDefaultServer);
        if (onOpenDefaultServerUsed) {
          onOpenDefaultServerUsed();
        }
      }
    }, [openDefaultServer, mcpServers, onOpenDefaultServerUsed]); // 依赖项包括 openDefaultServer, mcpServers 和 onOpenDefaultServerUsed

    const defaultOpenServer = (serverName: string) => {
      console.log('defaultOpenServer', serverName);
      const index = mcpServers.findIndex((server) => server.name === serverName);
      if (index !== -1) {
        toggleExpanded(index);
      }
    };

    const cleanLoading = (time = 500) => {
      setTimeout(() => {
        setIsLoading(false);
      }, time);
    };

    const toggleService = (index: number) => {
      const server = mcpServers[index];
      setIsLoading(true);
      mcpPostMessage(McpSendMsgType.TOGGLE_MCP_SERVER, {
        serverName: server.name,
        disabled: !server.disabled,
        projectType: server.isProject ? 'project' : '',
      });
      cleanLoading(500);
    };

    const toggleExpanded = (index: number) => {
      console.log('toggleExpanded index', index);
      const serverName = mcpServers[index].name;
      const newExpandedServers = new Set(expandedServers);
      if (newExpandedServers.has(serverName)) {
        newExpandedServers.delete(serverName);
      } else {
        newExpandedServers.add(serverName);
        // 默认显示工具标签
        if (!activeTab[serverName]) {
          //setActiveTab(prev => ({ ...prev, [serverName]: 'tools' }));
          setActiveTab({ [serverName]: 'tools' });
        }
      }
      setExpandedServers(newExpandedServers);
    };

    const switchTab = (serverName: string, tab: 'tools' | 'resources') => {
      // setActiveTab(prev => ({ ...prev, [serverName]: tab }));
      setActiveTab({ [serverName]: tab });
    };

    const handleRetry = (index: number) => {
      handleRestart(index);
    };

    const handleAIFix = (index: number) => {
      console.log('AI修复服务:', mcpServers[index].name);
    };

    const handleEdit = (index: number) => {
      const server = mcpServers[index];
      const config = server.config;
      const isProject = server.isProject;
      const selServer = {
        name: server.name,
        serverParam: config,
        isProject: isProject,
      } as McpNodeService;
      setSelectedServer(selServer);
      setIsModalVisible(true);
    };

    const handleCloseModal = () => {
      setIsModalVisible(false);
      setSelectedServer(null);
    };

    const handleInstalledSuccess = () => {
      fetchMcpConnectionServer();
      handleCloseModal();
    };

    const handleDelete = (index: number) => {
      setIsLoading(true);
      console.log(mcpServers[index].name, mcpServers[index].isProject);
      const isProject = mcpServers[index].isProject;
      const name = mcpServers[index].name;
      mcpPostMessage(McpSendMsgType.DELETE_MCP_SERVER, {
        serverName: name,
        projectType: isProject ? 'project' : '',
      });
      cleanLoading(500);
    };

    const handleAutoApproveChange = (serverIndex: number, toolIndex: number, autoApprove: boolean) => {
      const server = mcpServers[serverIndex];
      const tool = server.tools?.[toolIndex];

      if (!tool) return;

      // 更新本地状态
      const newMcpServers = [...mcpServers];
      if (newMcpServers[serverIndex].tools) {
        newMcpServers[serverIndex].tools[toolIndex] = {
          ...tool,
          autoApprove,
        };
      }
      setMcpServers(newMcpServers);

      // 发送变更请求 - 使用通用的服务器更新消息
      mcpPostMessage(McpSendMsgType.UPDATE_TOOL_AUTO_APPROVE, {
        serverName: server.name,
        toolName: tool.name,
        autoApprove,
        projectType: server.isProject ? 'project' : '',
      });

      console.log(`更新工具自动确认状态: ${server.name}.${tool.name} -> ${autoApprove}`);
    };
    /**
     *重启服务
     * @param index
     */
    const handleRestart = (index: number) => {
      const server = mcpServers[index];
      mcpPostMessage(McpSendMsgType.RESTART_MCP_SERVER, {
        serverName: server.name,
      });
    };

    const getStatusIcon = (status: string) => {
      switch (status) {
        case 'connected':
          return (
            <div className="status-indicator">
              <span
                className="icon iconfont icon-wanchengtianchong"
                style={{ fontSize: '12px', color: 'var(--vscode-testing-iconPassed)' }}
              ></span>
              {/* <div className="status-dot connected" title="已连接"></div> */}
              <span className="status-text">已连接</span>
            </div>
          );
        case 'connecting':
          return (
            <div className="status-indicator">
              <LottieAnimation
                animationData={waitingAnimation}
                style={{ width: '16px', height: '16px', marginRight: '4px' }}
                loop={true}
                autoplay={true}
              />
              <span className="status-text">正在启动...</span>
            </div>
          );
        case 'disconnected':
          return (
            <div className="status-indicator">
              {/* <div className="status-dot error" title="启动失败"></div> */}
              <span
                className="icon iconfont icon-shibai"
                style={{ fontSize: '12px', color: 'var(--vscode-testing-iconFailed)' }}
              ></span>
              <span className="status-text">启动失败</span>
            </div>
          );
        default:
          return (
            <div className="status-indicator">
              <div className="status-dot" title="未连接" style={{ color: 'var(--vscode-testing-iconFailed)' }}></div>
              <span className="status-text">未连接</span>
            </div>
          );
      }
    };

    const renderInputSchema = (schema: any) => {
      if (!schema || typeof schema !== 'object') {
        return <div className="schema-empty">无参数信息</div>;
      }

      const properties = schema.properties || {};
      const required = schema.required || [];

      if (Object.keys(properties).length === 0) {
        return <div className="schema-empty">无参数</div>;
      }

      return (
        <div className="schema-properties">
          {Object.entries(properties).map(([key, value]: [string, any]) => (
            <div key={key} className="schema-property">
              <div className="property-header">
                <span className="property-name">{key}</span>
                {required.includes(key) && <span className="property-required">*</span>}
                {value.type && <span className="property-type">({value.type})</span>}
              </div>
              {value.description && <div className="property-description">{value.description}</div>}
              {value.enum && (
                <div className="property-enum">
                  可选值:{' '}
                  {value.enum
                    .map((item: any, index: number) => (
                      <code key={index} className="enum-value">
                        {JSON.stringify(item)}
                      </code>
                    ))
                    .reduce(
                      (prev: any, curr: any, index: number) => (index === 0 ? [curr] : [...prev, ', ', curr]),
                      []
                    )}
                </div>
              )}
              {value.default !== undefined && (
                <div className="property-default">
                  默认值: <code>{JSON.stringify(value.default)}</code>
                </div>
              )}
              {value.examples && value.examples.length > 0 && (
                <div className="property-examples">
                  示例: <code>{JSON.stringify(value.examples[0])}</code>
                </div>
              )}
            </div>
          ))}
        </div>
      );
    };

    useImperativeHandle(ref, () => ({
      fetchMcpConnectionServer,
    }));

    return (
      <div className="mcp-installed-view">
        {isLoading && (
          <div className="mcp-installed-view__loading">
            <Spin />
          </div>
        )}
        {mcpServers.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">
              <span className="icon iconfont icon-mcp" style={{ fontSize: '48px' }}></span>
            </div>
            <p style={{ fontSize: '13px' }}></p>
            <p style={{ fontSize: '12px' }}>点击刷新按钮获取已配置的 MCP 服务，或前往MCP市场页面添加新的服务。</p>
          </div>
        ) : (
          mcpServers.map((server, index) => (
            <div key={`${server.name}-${index}`} className="server-item" style={{ opacity: server.disabled ? 0.6 : 1 }}>
              <div className="server-header">
                <div className="server-left">
                  <button
                    className={`expand-button ${expandedServers.has(server.name) ? 'expanded' : ''}`}
                    onClick={() => toggleExpanded(index)}
                  >
                    <svg width="12" height="12" viewBox="0 0 12 12">
                      <path d="M4 2L8 6L4 10" stroke="currentColor" strokeWidth="1.5" fill="none" />
                    </svg>
                  </button>
                  <span className="server-name">{server.name}</span>
                  <button className="server-center">{server.isProject ? '项目' : '全局'}</button>
                  {getStatusIcon(server.status)}
                </div>

                <div className="server-right">
                  {server.status === 'disconnected' && server.error && (
                    <div className="error-actions">
                      <button className="retry-button" onClick={() => handleRetry(index)}>
                        重试
                      </button>
                    </div>
                  )}
                  <button className="refresh-button" title="重启" onClick={() => handleRestart(index)}>
                    <span className="icon iconfont icon-shuaxin" style={{ fontSize: '16px' }}></span>
                  </button>
                  <Switch
                    style={{ marginRight: '20px' }}
                    size="small"
                    checked={!server.disabled}
                    onChange={() => toggleService(index)}
                  />
                </div>
              </div>

              {expandedServers.has(server.name) && (
                <div className="server-content">
                  {server.error ? (
                    <div>
                      <div style={{ display: 'flex', justifyContent: 'right' }}>
                        <div className="tools-right">
                          <button
                            className="edit-button"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEdit(index);
                            }}
                            title="编辑"
                          >
                            <span className="icon iconfont icon-bianji" style={{ fontSize: '16px' }}></span>
                          </button>
                          <button
                            className="delete-button"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDelete(index);
                            }}
                            title="删除"
                          >
                            <span
                              className="icon iconfont icon-shanchu"
                              style={{ fontSize: '16px', color: 'var(--vscode-editorGutter-deletedBackground)' }}
                            ></span>
                          </button>
                        </div>
                      </div>
                      <div className="server-description">{server.error}</div>
                    </div>
                  ) : null}

                  {(server.tools && server.tools.length > 0) || (server.resources && server.resources.length > 0) ? (
                    <div className="server-tools">
                      <div className="tools-header">
                        <div className="tools-left">
                          <div className="tab-buttons">
                            <TabButton
                              active={activeTab[server.name] === 'tools'}
                              onClick={() => switchTab(server.name, 'tools')}
                            >
                              工具 ({server.tools?.length || 0})
                            </TabButton>
                            <TabButton
                              active={activeTab[server.name] === 'resources'}
                              onClick={() => switchTab(server.name, 'resources')}
                            >
                              资源 ({server.resources?.length || 0})
                            </TabButton>
                          </div>
                        </div>
                        <div className="tools-right">
                          <button
                            className="edit-button"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEdit(index);
                            }}
                            title="编辑"
                          >
                            <span className="icon iconfont icon-bianji" style={{ fontSize: '16px' }}></span>
                          </button>
                          <button
                            className="delete-button"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDelete(index);
                            }}
                            title="删除"
                          >
                            <span
                              className="icon iconfont icon-shanchu"
                              style={{ fontSize: '16px', color: 'var(--vscode-editorGutter-deletedBackground)' }}
                            ></span>
                          </button>
                        </div>
                      </div>

                      {/* 工具列表 */}
                      {(activeTab[server.name] || 'tools') === 'tools' && server.tools && (
                        <div className="tools-list">
                          {server.tools.map((tool, toolIndex) => (
                            <div key={`${tool.name}-${toolIndex}`} className="tool-item">
                              <div className="tool-header">
                                <div className="tool-name">{tool.name}</div>
                                <div className="tool-auto-approve">
                                  <label className="auto-approve-checkbox">
                                    <input
                                      type="checkbox"
                                      checked={tool.autoApprove || false}
                                      onChange={(e) => handleAutoApproveChange(index, toolIndex, e.target.checked)}
                                    />
                                    <span className="checkbox-label">自动确认</span>
                                  </label>
                                </div>
                              </div>
                              <div className="tool-description">{tool.description}</div>
                              {tool.inputSchema && (
                                <div className="tool-schema">
                                  <div className="schema-title">参数</div>
                                  <div className="schema-content">{renderInputSchema(tool.inputSchema)}</div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}

                      {/* 资源列表 */}
                      {activeTab[server.name] === 'resources' && server.resources && (
                        <div className="resources-list">
                          {server.resources.map((resource, resourceIndex) => (
                            <div key={`${resource.uri}-${resourceIndex}`} className="resource-item">
                              <div className="resource-header">
                                <div className="resource-name">{resource.name}</div>
                                <div className="resource-uri">{resource.uri}</div>
                              </div>
                              {resource.description && (
                                <div className="resource-description">{resource.description}</div>
                              )}
                              {resource.mimeType && <div className="resource-mime-type">类型: {resource.mimeType}</div>}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ) : null}
                </div>
              )}
            </div>
          ))
        )}
        <McpModal
          isModalVisible={isModalVisible}
          isManualConfigModalVisible={false}
          selectedService={selectedServer}
          advancedConfig={false}
          onClose={handleCloseModal}
          onInstalledSuccess={handleInstalledSuccess}
        />
      </div>
    );
  }
);

export default McpInstalledView;
