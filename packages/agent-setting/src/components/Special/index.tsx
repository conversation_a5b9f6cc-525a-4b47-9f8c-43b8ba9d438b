import React from 'react';
import { Checkbox, Select, Slider, SliderSingleProps } from 'antd';
import { CheckboxChangeEvent } from 'antd/lib/checkbox/Checkbox';

interface SpecialTabProps {
  completionDelay: number;
  codeCompletionsMoreContext: boolean;
  isShowCodeLens: boolean;
  codeLens: string[];
  commitMessage: string;
  isCommitCodeReview: boolean;
  isErrorLine: boolean;
  marks: SliderSingleProps['marks'];
  styles: {
    track: { backgroundColor: string };
    tracks: { background: string; border: string };
    rail: { background: string };
  };
  onCompletionDelayChange: (value: number) => void;
  onCompletionsMoreContextChange: (e: CheckboxChangeEvent) => void;
  onCodeLensChange: (e: CheckboxChangeEvent) => void;
  onCommitMessageChange: (value: string) => void;
  onCommitCodeReviewChange: (e: CheckboxChangeEvent) => void;
  onErrorLineChange: (e: CheckboxChangeEvent) => void;
}

export default function SpecialTab({
  completionDelay,
  codeCompletionsMoreContext,
  isShowCodeLens,
  codeLens,
  commitMessage,
  isCommitCodeReview,
  isErrorLine,
  marks,
  styles,
  onCompletionDelayChange,
  onCompletionsMoreContextChange,
  onCodeLensChange,
  onCommitMessageChange,
  onCommitCodeReviewChange,
  onErrorLineChange,
}: SpecialTabProps) {
  return (
    <div className="setting-tabs-item">
      <div className="setting-tabs-item-title">特性</div>
      <div className="joycoder-setting-box">
        <div className="joycoder-setting-box-title">预测补全</div>
        <div className="joycoder-setting-box-text">生成延迟时间，避免不必要的建议</div>
        <Slider
          marks={marks}
          step={0.1}
          value={completionDelay}
          min={0.3}
          max={3}
          onChange={onCompletionDelayChange}
          trackStyle={styles.track}
          handleStyle={styles.tracks}
          railStyle={styles.rail}
        />
        <div className="joycoder-setting-box-title mt-18">偏好设置</div>
        <div className="joycoder-setting-box-text">
          <Checkbox
            className="joycoder-setting-box-context"
            checked={codeCompletionsMoreContext}
            onChange={onCompletionsMoreContextChange}
          >
            启用跨文件感知
          </Checkbox>
        </div>
        <div className="joycoder-setting-box-tips">
          注：启用后即可感知当前文件目录及打开的相似文件作为预测补全的上下文
        </div>
      </div>
      <div className={!isShowCodeLens ? 'joycoder-setting-box mb-16 hidden' : 'joycoder-setting-box mb-16'}>
        <div className="joycoder-setting-box-title mt-26">行间菜单</div>
        <div>
          <div className="joycoder-setting-box-text">
            <Checkbox
              className="joycoder-setting-box-context"
              checked={codeLens.includes('functionComment')}
              onChange={onCodeLensChange}
              value="functionComment"
            >
              开启生成【函数注释】行间展示
            </Checkbox>
          </div>
          <div className="joycoder-setting-box-text">
            <Checkbox
              className="joycoder-setting-box-context"
              checked={codeLens.includes('reconstruction')}
              onChange={onCodeLensChange}
              value="reconstruction"
            >
              开启生成【代码重构】行间展示
            </Checkbox>
          </div>
          <div className="joycoder-setting-box-text">
            <Checkbox
              className="joycoder-setting-box-context"
              checked={codeLens.includes('comment')}
              onChange={onCodeLensChange}
              value="comment"
            >
              开启生成【逐行注释】行间展示
            </Checkbox>
          </div>
          <div className="joycoder-setting-box-text">
            <Checkbox
              className="joycoder-setting-box-context"
              checked={codeLens.includes('codeReview')}
              onChange={onCodeLensChange}
              value="codeReview"
            >
              开启生成【代码评审】行间展示
            </Checkbox>
          </div>
          <div className="joycoder-setting-box-text mb-16">
            <Checkbox
              className="joycoder-setting-box-context"
              checked={codeLens.includes('test')}
              onChange={onCodeLensChange}
              value="test"
            >
              开启生成【单元测试】行间展示
            </Checkbox>
          </div>
        </div>
      </div>
      <div className="joycoder-setting-box mb-16">
        <div className="joycoder-setting-box-title">生成Commit Message</div>
        <div className="joycoder-setting-box-text joycoder-flex w-88 mb-16">
          <div>
            <span className="joycoder-setting-box-label w-88">生成模式配置：</span>
          </div>
          <div className="joycolder-setting-box-select-wrap">
            <Select
              className="joycoder-setting-box-select"
              value={commitMessage}
              onChange={onCommitMessageChange}
              placeholder="JoyCoder生成Commit Message配置"
              size="small"
              options={[
                {
                  value: 'GIT_SCHEMA',
                  label: (
                    <>
                      <div>标准模式</div>
                      <div className="joycoder-setting-box-message">根据 Conventional Commits 规范生成；</div>
                      <div className="joycoder-setting-box-message">示例：fix(chat): 接口错误</div>
                    </>
                  ),
                },
                {
                  value: 'BRANCH_SCHEMA',
                  label: (
                    <>
                      <div>分支模式</div>
                      <div className="joycoder-setting-box-message">生成变更摘要 + 分支名称;</div>
                      <div className="joycoder-setting-box-message">示例：fix: 接口错误[分支名称]</div>
                    </>
                  ),
                },
                {
                  value: 'AUTO',
                  label: (
                    <>
                      <div>变更摘要</div>
                      <div className="joycoder-setting-box-message">示例：fix connection error</div>
                    </>
                  ),
                },
              ]}
            />
          </div>
        </div>
      </div>
      <div className="joycoder-setting-box mb-24">
        <div className="joycoder-setting-box-title">代码评审</div>
        <div className="mb-16 v-h mt-10">
          <div className="joycoder-setting-box-text">
            <Checkbox
              className="joycoder-setting-box-context"
              checked={isCommitCodeReview}
              onChange={onCommitCodeReviewChange}
            >
              开启代码评审增量扫描
            </Checkbox>
          </div>
          <div className="joycoder-setting-box-tip">
            注：针对每次提交代码进行质量、安全、编码规范、逻辑错误等多维度扫描，并提供修复建议；评审结果在问答窗展示。
          </div>
        </div>
        <div className="mb-16 v-h">
          <div className="joycoder-setting-box-text">
            <Checkbox className="joycoder-setting-box-context" checked={isErrorLine} onChange={onErrorLineChange}>
              开启行间错误提示
            </Checkbox>
          </div>
          <div className="joycoder-setting-box-tip">
            注：指编辑过程中分析存在的质量、安全等问题；直接在编辑区区域红字提示。
          </div>
        </div>
      </div>
    </div>
  );
}
