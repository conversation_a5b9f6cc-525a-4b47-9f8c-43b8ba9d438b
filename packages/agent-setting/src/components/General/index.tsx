import React from 'react';
import { Button } from 'antd';

interface GeneralTabProps {
  userName: string;
  isIDE: boolean;
  onLogout: () => void;
  onMoreSetting: () => void;
}

export default function GeneralTab({ userName, isIDE, onLogout, onMoreSetting }: GeneralTabProps) {
  return (
    <div className="setting-tabs-item">
      <div className="setting-tabs-item-title">通用</div>
      <div className="joycoder-setting-box">
        <div className="joycoder-setting-box-title">账号</div>
        <div className="joycoder-setting-box-text">当前登录账号： {userName}</div>
        <div className="joycoder-setting-btns">
          <div>
            <Button type="primary" size="small" onClick={onLogout}>
              <span className="icon iconfont icon-dengchu"> 登出</span>
            </Button>
          </div>
        </div>
      </div>
      <div className="joycoder-setting-box">
        <div className="joycoder-setting-box-title mt-18">{isIDE ? 'IDE' : 'VSCode'} 设置</div>
        <div className="joycoder-setting-box-text">对于常规编辑器设置，请访问编辑器设置页面</div>
        <div className="joycoder-setting-btns">
          <div>
            <Button type="primary" size="small" onClick={onMoreSetting}>
              去设置
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
