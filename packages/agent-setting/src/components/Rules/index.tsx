import React from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Input, Space, Tag, Select, Modal, Tooltip } from 'antd';
import { CommonMessage } from '../../messages/messageTypes';

const { TextArea } = Input;

interface RulesTabProps {
  customInstructions: string;
  isShowAddRule: boolean;
  modalTitle: string;
  projectRuleName: string;
  ruleType: string;
  filePatterns: string[];
  filePatternInput: string;
  onCustomInstructionsChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onShowAddRule: (show: boolean) => void;
  onProjectRuleNameChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onRuleTypeChange: (value: string) => void;
  onFilePatternInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onAddFilePattern: () => void;
  onRemoveFilePattern: (pattern: string) => void;
  onCreateProjectRule: () => void;
}

export default function RulesTab({
  customInstructions,
  isShowAddRule,
  modalTitle,
  projectRuleName,
  ruleType,
  filePatterns,
  filePatternInput,
  onCustomInstructionsChange,
  onShowAddRule,
  onProjectRuleNameChange,
  onRuleTypeChange,
  onFilePatternInputChange,
  onAddFilePattern,
  onRemoveFilePattern,
  onCreateProjectRule,
}: RulesTabProps) {
  const handleCreateRuleClick = () => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'setting-McpHub',
        data: {},
      },
    });
    onShowAddRule(true);
  };

  return (
    <div className="setting-tabs-item">
      <div className="setting-tabs-item-title">规则</div>
      <div className="joycoder-setting-box">
        <div className="joycoder-setting-box-title">用户规则</div>
        <div className="joycoder-setting-box-text mb-16 l-20">
          用户规则适用于所有用户，并且始终包含在模型上下文中。您可以在此定义使用习惯，例如：输出语言、代码生成时使用行间注释等，重启IDE后生效
        </div>
        <TextArea
          className="joycoder-setting-textarea mt-12"
          value={customInstructions}
          onChange={onCustomInstructionsChange}
          placeholder="使用中文回复"
          autoSize={{ minRows: 2, maxRows: 6 }}
        />
      </div>

      <div className="joycoder-setting-box">
        <div className="joycoder-setting-box-title mt-18">项目规则</div>
        <div className="joycoder-setting-box-text l-20">
          项目规则可以帮助 AI
          理解您的代码库并遵循您定义的规则。这些规则可以始终包含在上下文中，或智能体根据规则主动获取。
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleCreateRuleClick}
          style={{
            backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
            borderColor: 'var(--vscode-input-border, #72747c)',
            width: '106px',
            height: '28px',
            marginTop: '8px',
            borderRadius: '4px',
            color: 'var(--vscode-input-foreground, #72747c)',
            fontSize: '12px',
            padding: '0',
          }}
        >
          创建规则文件
        </Button>
      </div>
      <Modal
        title={modalTitle}
        open={isShowAddRule}
        onCancel={() => onShowAddRule(false)}
        onOk={onCreateProjectRule}
        okText="保存"
        cancelText="取消"
      >
        <div className="joycoder-setting-box-text mb-4">规则名称</div>
        <Input
          placeholder="输入规则名称，用于创建文件"
          value={projectRuleName}
          onChange={onProjectRuleNameChange}
          className="joycoder-dark-input-border mb-4"
        />

        <div className="joycoder-setting-box-text mb-4">规则类型</div>
        <Select
          className="joycoder-setting-box-select mb-4"
          style={{ width: '100%' }}
          value={ruleType}
          onChange={onRuleTypeChange}
          options={[
            { value: 'Always', label: '始终生效' },
            { value: 'AutoAttached', label: '仅指定文件生效' },
          ]}
        />

        {ruleType === 'AutoAttached' && (
          <>
            <div className="joycoder-setting-box-text mb-4">
              匹配规则{' '}
              <Tooltip
                title={
                  <span style={{ color: '#303133FF', fontSize: '12px', lineHeight: '18px' }}>
                    当您在此处指定文件模式时（例如 *.ts，src/config/***json），此规则将自动包含在提问或智能体的上下文中
                  </span>
                }
                color="#FFFFFFFF"
              >
                <i className="icon iconfont icon-tanhao"></i>
              </Tooltip>
            </div>
            <div className="mb-4">
              {filePatterns.map((pattern) => (
                <Tag
                  key={pattern}
                  closable
                  onClose={() => onRemoveFilePattern(pattern)}
                  style={{ marginBottom: '8px', marginRight: '8px' }}
                >
                  {pattern}
                </Tag>
              ))}
            </div>
            <Space.Compact style={{ width: '100%' }}>
              <Input
                className="joycoder-dark-input-border"
                placeholder="例如：*.ts，src/config/***json"
                value={filePatternInput}
                onChange={onFilePatternInputChange}
                onPressEnter={onAddFilePattern}
              />
              <Button icon={<PlusOutlined />} onClick={onAddFilePattern}>
                添加
              </Button>
            </Space.Compact>
          </>
        )}
      </Modal>
    </div>
  );
}
