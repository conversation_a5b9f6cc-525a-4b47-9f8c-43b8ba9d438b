# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [4.0.0](///compare/v1.26.0...v4.0.0) (2025-07-04)


### Features

* **自动化编程:** 代码修改方式增加差异和自动拆分模式&优化已知问题 497c244
* **自动化编程:** 增加自动化编程 9650cad
* **自动化编程:** 增加自动化编程模式 0d7a972
* **自动化编程:** 自动化编程升级 &已知问题修复 e578ec0
* **Agent编程:**  窗口合并 & codebase & mcp优化 0852b9d
* **Agent:** 智能体团队&自定义智能体&UI升级&内置浏览器&自定义提示词 ([#23](undefined/undefined/undefined/issues/23)) f67715e
* **chat:** 优化聊天界面和搜索功能 e1d987c
* **Coder:** 支持远程环境&支持多rule&支持联网搜索&支持Coder模式切换 3b69626


### Bug Fixes

* **对话处理:** 清理全局状态命令 f61d65c
* **自动化编程:** 优化终端运行卡顿问题 3b1e5ea
* **Agent编程:** 联网搜索适配新模型返回结果 ([#20](undefined/undefined/undefined/issues/20)) 7697b15
* **Coder:**  mermaid支持 44db1ff
* **Coder:** 未登录不提示登录问题修复&运行命令按钮出现多次问题修复&卡死问题修复 ([#5](undefined/undefined/undefined/issues/5)) e3a231a
* **config:** 切换配置拉取的域名 e30a052
* **IDE:** 移除不再使用的市场插件依赖 ([#3](undefined/undefined/undefined/issues/3)) 912cee3

## [2.9.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.26.0...v2.9.0) (2025-05-23)


### Features

* **自动化编程:** 代码修改方式增加差异和自动拆分模式&优化已知问题 ([497c244](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/497c2442ebfa502431b0c7654bb009e0cb6eb89b))
* **自动化编程:** 增加自动化编程 ([9650cad](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9650cadca6c0a033cefb326c99701a231ed2db59))
* **自动化编程:** 增加自动化编程模式 ([0d7a972](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0d7a972b0523d575b0d878fcd225c552f0e01b38))
* **自动化编程:** 自动化编程升级 &已知问题修复 ([e578ec0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e578ec0473ab3421384c314bff9addd5ac4c5d7b))
* **Agent编程:**  窗口合并 & codebase & mcp优化 ([0852b9d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0852b9dd03e359593d922ffd378846794eaf8c51))
* **chat:** 优化聊天界面和搜索功能 ([e1d987c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e1d987ce2ea223ea5fbf4250971694324f968284))
* **Coder:** 支持远程环境&支持多rule&支持联网搜索&支持Coder模式切换 ([3b69626](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3b696261047f944a0b08cbf95fb2cef75538bb7e))


### Bug Fixes

* **对话处理:** 清理全局状态命令 ([f61d65c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f61d65c436be96671a3cdf6ba7ee81748b5603bf))
* **自动化编程:** 优化终端运行卡顿问题 ([3b1e5ea](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3b1e5ead29f07395c70ac4a8846c4dc7b7691b20))
* **Agent编程:** 联网搜索适配新模型返回结果 ([#20](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/20)) ([7697b15](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7697b154eec3854cdecf209fb911d8c1d9d262af))
* **Coder:**  mermaid支持 ([44db1ff](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/44db1ff96ae87b245d7af93e534ebc66592ff66f))
* **Coder:** 未登录不提示登录问题修复&运行命令按钮出现多次问题修复&卡死问题修复 ([#5](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/5)) ([e3a231a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e3a231ac152756054ad8c7912a8494afdff88466))
* **config:** 切换配置拉取的域名 ([e30a052](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e30a05240e6fe32549a0f36406c750f4a55bc6cf))
* **IDE:** 移除不再使用的市场插件依赖 ([#3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/3)) ([912cee3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/912cee36e0a7c24ef696bb0ee93852909439f498))

## [2.8.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.26.0...v2.8.0) (2025-04-17)


### Features

* **自动化编程:** 代码修改方式增加差异和自动拆分模式&优化已知问题 ([497c244](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/497c2442ebfa502431b0c7654bb009e0cb6eb89b))
* **自动化编程:** 增加自动化编程 ([9650cad](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9650cadca6c0a033cefb326c99701a231ed2db59))
* **自动化编程:** 增加自动化编程模式 ([0d7a972](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0d7a972b0523d575b0d878fcd225c552f0e01b38))
* **自动化编程:** 自动化编程升级 &已知问题修复 ([e578ec0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e578ec0473ab3421384c314bff9addd5ac4c5d7b))
* **chat:** 优化聊天界面和搜索功能 ([e1d987c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e1d987ce2ea223ea5fbf4250971694324f968284))
* **Coder:** 支持远程环境&支持多rule&支持联网搜索&支持Coder模式切换 ([3b69626](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3b696261047f944a0b08cbf95fb2cef75538bb7e))


### Bug Fixes

* **对话处理:** 清理全局状态命令 ([f61d65c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f61d65c436be96671a3cdf6ba7ee81748b5603bf))
* **自动化编程:** 优化终端运行卡顿问题 ([3b1e5ea](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3b1e5ead29f07395c70ac4a8846c4dc7b7691b20))
* **Coder:**  mermaid支持 ([44db1ff](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/44db1ff96ae87b245d7af93e534ebc66592ff66f))
* **Coder:** 未登录不提示登录问题修复&运行命令按钮出现多次问题修复&卡死问题修复 ([#5](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/5)) ([e3a231a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e3a231ac152756054ad8c7912a8494afdff88466))
* **config:** 切换配置拉取的域名 ([e30a052](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e30a05240e6fe32549a0f36406c750f4a55bc6cf))
* **IDE:** 移除不再使用的市场插件依赖 ([#3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/3)) ([912cee3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/912cee36e0a7c24ef696bb0ee93852909439f498))

## [2.7.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.26.0...v2.7.0) (2025-03-17)


### Features

* **自动化编程:** 代码修改方式增加差异和自动拆分模式&优化已知问题 ([497c244](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/497c2442ebfa502431b0c7654bb009e0cb6eb89b))
* **自动化编程:** 增加自动化编程 ([9650cad](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9650cadca6c0a033cefb326c99701a231ed2db59))
* **自动化编程:** 增加自动化编程模式 ([0d7a972](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0d7a972b0523d575b0d878fcd225c552f0e01b38))
* **自动化编程:** 自动化编程升级 &已知问题修复 ([e578ec0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e578ec0473ab3421384c314bff9addd5ac4c5d7b))


### Bug Fixes

* **对话处理:** 清理全局状态命令 ([f61d65c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f61d65c436be96671a3cdf6ba7ee81748b5603bf))
* **自动化编程:** 优化终端运行卡顿问题 ([3b1e5ea](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3b1e5ead29f07395c70ac4a8846c4dc7b7691b20))
* **Coder:**  mermaid支持 ([44db1ff](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/44db1ff96ae87b245d7af93e534ebc66592ff66f))
* **Coder:** 未登录不提示登录问题修复&运行命令按钮出现多次问题修复&卡死问题修复 ([#5](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/5)) ([e3a231a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e3a231ac152756054ad8c7912a8494afdff88466))
* **config:** 切换配置拉取的域名 ([e30a052](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e30a05240e6fe32549a0f36406c750f4a55bc6cf))
* **IDE:** 移除不再使用的市场插件依赖 ([#3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/3)) ([912cee3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/912cee36e0a7c24ef696bb0ee93852909439f498))

## [2.3.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.26.0...v2.3.0) (2025-01-17)


### Features

* **自动化编程:** 代码修改方式增加差异和自动拆分模式&优化已知问题 ([497c244](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/497c2442ebfa502431b0c7654bb009e0cb6eb89b))
* **自动化编程:** 增加自动化编程 ([9650cad](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9650cadca6c0a033cefb326c99701a231ed2db59))
* **自动化编程:** 增加自动化编程模式 ([0d7a972](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0d7a972b0523d575b0d878fcd225c552f0e01b38))


### Bug Fixes

* **自动化编程:** 优化终端运行卡顿问题 ([3b1e5ea](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3b1e5ead29f07395c70ac4a8846c4dc7b7691b20))
* **config:** 切换配置拉取的域名 ([e30a052](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e30a05240e6fe32549a0f36406c750f4a55bc6cf))

## [2.2.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.26.0...v2.2.0) (2024-12-31)


### Features

* **自动化编程:** 增加自动化编程 ([9650cad](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9650cadca6c0a033cefb326c99701a231ed2db59))
* **自动化编程:** 增加自动化编程模式 ([0d7a972](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0d7a972b0523d575b0d878fcd225c552f0e01b38))


### Bug Fixes

* **自动化编程:** 优化终端运行卡顿问题 ([3b1e5ea](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3b1e5ead29f07395c70ac4a8846c4dc7b7691b20))
* **config:** 切换配置拉取的域名 ([e30a052](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e30a05240e6fe32549a0f36406c750f4a55bc6cf))

## [2.1.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.26.0...v2.1.0) (2024-12-27)


### Features

* **自动化编程:** 增加自动化编程模式 ([0d7a972](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0d7a972b0523d575b0d878fcd225c552f0e01b38))

## [1.26.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.25.1...v1.26.0) (2024-12-13)


### Features

* **代码补全:**  支持主流语言跨文件感知import导入 ([e8baf48](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e8baf4820402e0bbe8eb8966fb030c6f79dd7eba))
* **代码评审:** 添加通天塔代码评审规则检测 ([f2dc4c7](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f2dc4c745f192e033fa13ddbee6048df71b9bca6))


### Bug Fixes

* **智能助手:** 优化交互，修正进度处理和日志输出，展示思考过程 ([1db80d5](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1db80d5eef5152497314ceab4d1a0f9081adeff5))
* **codeLens:** 修复CodeLens菜单初始化逻辑 ([08d907b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/08d907b06300f3d8b5a9cb291a55661a2fd5625b))

### [1.25.1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.25.0...v1.25.1) (2024-11-26)


### Bug Fixes

* **config:** 修复默认的远程配置url ([86176bc](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/86176bc4810e4f562f538c5107ce2fbd68bab350))

## [1.25.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.24.4...v1.25.0) (2024-11-25)


### Features

* **聊天窗口:** 常用设置页面&支持表格展示&修复异常问题 ([ff87544](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ff87544cd918ef06859a1c6e5a5f6644e0c2dadf))
* **聊天窗口:** 增加提示词优化功能 ([3554bbb](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3554bbb71cd82d445d0404275172c7199e3b3b0f))
* **统计:** 添加文件变更监控功能 ([9ea0c0e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9ea0c0edfe49ad4c29381b81031b04ff96ce43c4))


### Bug Fixes

* **gitignore:** gitignore ([463b1ce](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/463b1ceb23e207eac89b8e45995fb9992e1857ce))

### [1.24.4](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.24.3...v1.24.4) (2024-10-31)


### Bug Fixes

* **代码补全:**  支持注释生成 ([ceb561a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ceb561a5199aa254d358c7e7f7de9f090f7b122e))
* **代码评审:** 增加定时检测能力 ([8f27e02](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8f27e0287d471373722efa714263f6fa4ae6bdc4))
* **remote:** 修复AdoptResultCache.setRemote参数错误并添加批量翻译支持 && 连续补全场景不限制类 ([35bcdf5](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/35bcdf5600d5ec2f10e40270b83ae2a96febf846))

### [1.24.3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.24.2...v1.24.3) (2024-10-24)


### Bug Fixes

* **refactor(codeStatService):** 添加PATH环境变量到envInfo对象中 ([c9484ce](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c9484cefa5b831181789495ded9ace5091e363b8))

### [1.24.2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.24.1...v1.24.2) (2024-10-12)


### Bug Fixes

* **代码评审:** 修正文件来源枚举值错误&为消息添加唯一标识messageId以增强追踪能力 ([#100](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/100)) ([bbedcc9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/bbedcc971c0333ae4933a6d63d04c5da3596dc0a))

### [1.24.1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.24.0...v1.24.1) (2024-10-11)


### Bug Fixes

* **代码补全:** 修复completionId获取逻辑以处理嵌套数据结构 ([3714df8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3714df85acadd7a13e4eef738e094b358a05a8c2))

## [1.24.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.23.0...v1.24.0) (2024-09-27)


### Features

* **仓库问答:** 仓库问答&D2C&代码翻译支持C#转Java ([304105f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/304105fddbcff396dbbb8037ccad6aba7b06d447))


### Bug Fixes

* **仓库问答:** 处理仓库问答异常边界情况&&优化二进制管理逻辑 ([9fc7b2c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9fc7b2c676c4ff0ed4ed09d3892f56e1dae13998))
* **仓库问答:** 修复日志输出和标签替换问题 ([78b038d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/78b038da56bf1975a32351e2f34f2f9e472f8936))
* **聊天窗:** 更换图片上传url，并支持后续配置下发 ([a620207](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a620207b7261b96025d2b439f056ed1187a62836))

## [1.23.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.22.1...v1.23.0) (2024-09-06)


### Features

* **代码补全:** 代码补全跨文件感知优化并且增加自动触发补全功&批量导出历史记录等 ([9b4543b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9b4543bbf2f831deabb76679a3e6ec7362e23f75))


### Bug Fixes

* **代码补全:** 修复Vue2行间菜单展示 ([fc3c0d9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fc3c0d9c4afad43138195ea272b068dd963691e1))

### [1.22.1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.22.0...v1.22.1) (2024-08-26)


### Bug Fixes

* **web:** 修复panel可能未定义的错误 ([ddb1637](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ddb163761be677dd4a8237184c644b7b57fb14aa))

## [1.22.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.21.0...v1.22.0) (2024-08-23)


### Features

* **聊天框:** 支持数学公式显示 ([4b99856](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4b9985649be7df9a56431b46520fcbe92ca5f318))
* **聊天框:** 支持一键关闭所有的tab/添加点赞和点踩功能/代码翻译优化/添加窗口输入框焦点 ([#77](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/77)) ([978016c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/978016caf4ea56829fc5a57053f03e7d6b591312))


### Bug Fixes

* **代码补全:** 修复补全接受注释生成代码时光标问题 ([55632bf](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/55632bf848071572f947e7b042914c6610de0cb4))
* **代码重构:** 优化代码重构指引文本，提高可读性和详细性 ([d918ebb](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d918ebbb317780a43fa46b685ced05484e3d0c33))
* **智能助手:** 指定计划时增加预计时间提示 ([2b91785](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/2b91785b6ba31c365b39c7083aa8bd9fda9cc977))
* **report:** 数据上报问题修复 ([b37cdc1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b37cdc1f7ee0d5622af4b61b549bde8ad30f5c04))

## [1.21.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.20.0...v1.21.0) (2024-08-09)


### Features

* **文生图表:** mermaid流程图显示优化 ([a82121c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a82121c1b5b0319467b03b470c2e140329284442))
* **智能助手:** 基于多Agent的智能助手，支持联网搜索、计算等常用工具 ([3d09301](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3d093013e6596465a03d2a575e24989553512243))
* **智能助手:** 限制3个计划，防止等待时间过久 ([0a81434](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0a81434c6258b85522c5399fe7277ea734e2572e))
* **智能助手:** 依赖升级 ([b67f9ef](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b67f9efb665c804268bf4ed30e56c510b371995f))


### Bug Fixes

* **代码补全:**  增加行间菜单代码重构、逐行注释&代码补全增加逐行采纳&批量操作日志上报 ([a0fa629](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a0fa6299504ce7dd2a34a7d6443de57017bbc93e))
* **快速创建:** 修复快速创建页面白屏问题 ([c04a1e6](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c04a1e6dd4da83f3135be6f41b28b611591b73b2))
* **知识库:** 文件上传失败展示详细信息 ([dbd8331](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/dbd833183e147a7346790a3f2700db4179310c06))
* **sec:** find_vuln_deep接口code字段补充语言信息 ([3c33715](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3c33715e43706ec0275d2745e85cc5594641b5ba))

## [1.20.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.17.1...v1.20.0) (2024-07-22)


### Features

* **仓库问答:** 支持@文件和文件夹，升级基础依赖 ([e399d67](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e399d676f8fee012e7cb63ca3256d1f2f230cfc7))
* **代码评审:** 添加代码评审结果缓存功能,用于后续AI代码占比统计 ([d1c73c3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d1c73c3fadfffdb182bf648b0606de8485de48a7))
* **代码评审:** 主动代码评审：行间菜单添加代码评审，增加errorline以及切换文件后进行主动评审 ([54e470b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/54e470bb984f53209cc77f2a465d1a68f7dac148))
* **行间菜单:**   函数生成注释添加行间菜单loading ([3daf69e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3daf69e45ad9cbb2c8e6424077b878578de483cd))
* **ai代码占比:** AI生成代码占比统计 ([41da4be](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/41da4be0f8911f8547d09d7acfc19f57759af98c))
* **context:** 读取文件目录内容 ([4ac6382](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4ac638226bacf90afddb650940939c36d139a15f))
* **context:** 支持不主动选择的召回 ([1cb3dd4](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1cb3dd4b0106e1f83d3c58b70df42b80aacd6dc5))
* **context:** 重构查询文件&文件夹实现 ([6fc5575](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6fc55751bca7e04a0b298908f8ed862a3f64a070))
* **CR:**  添加行间错误提示配置 ([187f87a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/187f87aca624c989b883c1e918a213f9c9bb8ffe))
* **D2C:** 添加对`HuaMei`模块的支持,包括配置和生成方式的更新 ([0f3fc67](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0f3fc6769b4b7921809c29461ab00bf51d900597))


### Bug Fixes

* **补全日志:**    添加跨文件夹感知上报 ([80005fd](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/80005fd6b1e7ffdd89e3be59150fee8da46efb43))
* **补全日志:**    添加跨文件夹感知上报 ([4bb1859](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4bb1859c79d59d4b5847459fda87a75053d1996b))
* **代码评审:**  优化评审提示词 ([a0e243c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a0e243cc2bb1470f60375e5ef9880c7efad1575b))
* **代码评审:**  优化评审提示词 ([5b8ae7e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5b8ae7ed0802791560a091dc0ed900c4b7deae19))
* **代码评审:** 代码评审添加日志上报功能 ([adb5aac](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/adb5aac0e7c61f74ae99ded3d6ee956ba58d1453))
* **代码评审日志:** 添加代码评审日志上报逻辑 ([1d4a6d1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1d4a6d19bae3fa09ea1cc8a7a0711fe3a77554d8))
* **代码评审日志:** 添加代码评审上报逻辑 ([644036d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/644036d3f4515aca53068efd7508377c31209759))
* **代码评审日志:** 添加代码评审上报逻辑 ([0b4f03d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0b4f03d7df5ba1d9e335288f6dbba7191926f1bf))
* **来聊天窗:** 内部版本去除默认模型 ([767eb63](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/767eb63fc10966263d1b4abf27d05ef3d6c93b85))
* **评审日志:**  修改代码评审结果缓存时机 ([9ec8e2d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9ec8e2d990ea4d4556f5de5a94d6a4fbacfbac12))
* **chat:**  VUE 代码高亮bug修复 ([4ac4aa7](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4ac4aa77fb7a716362c8884321c53ddd68879753))
* **context:**  补充日志和耗时记录 ([06335da](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/06335dac5ca09078a97a491a68b2605360c48ab6))
* **context:**  目录查询层级优化&避免性能问题 ([a1c4f1d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a1c4f1de4c09cc920bdb8736d342c723a98f7afc))
* **context:**  目录召回先校验判断，避免性能问题 ([1f45c6c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1f45c6c443706e8b33687091b53efe9f85a3d61b))
* **context:**  新增清楚学习记录的按钮 ([cbf0653](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/cbf065360f87f6c5c5de0e516d792ef525154179))
* **context:**  UI优化，点击目录去重并过滤.git ([f443a46](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f443a46f002d1bc54fddc6bfe9db9444ec1e6d2a))
* **context:** 兼容windows系统的path处理 ([b9bf06b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b9bf06b5ae042720856f5cc814457c4f04a53f0f))
* **context:** 修复读取文件超时返回undefined ([33e4a94](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/33e4a9411e5d093482156b201b3afd429c57f17d))
* **context:** 优化文件过长时，弹层闪动的问题 ([fc641a3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fc641a3e5683b4db7ec4ddb6f9259495402029fd))
* **context:** 优化召回异常的逻辑 ([7f1edbf](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7f1edbf831ed71ea5a00818948b927a9593fd651))
* **D2C:** 修改生成按钮位置 ([3e4e102](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3e4e1024b15f661c970f707b3590655d0c17196f))
* **report:** 优化ctrlC操作只上报大模型回答节点 ([#55](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/55)) ([8870148](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8870148775e39ce66cbb114df2ebc901c76274e1))

## [1.19.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.18.0...v1.19.0) (2024-07-05)


### Features

* **代码评审:** 主动代码评审：行间菜单添加代码评审，增加errorline以及切换文件后进行主动评审 ([54e470b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/54e470bb984f53209cc77f2a465d1a68f7dac148))
* **行间菜单:**   函数生成注释添加行间菜单loading ([3daf69e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3daf69e45ad9cbb2c8e6424077b878578de483cd))
* **CR:**  添加行间错误提示配置 ([187f87a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/187f87aca624c989b883c1e918a213f9c9bb8ffe))


### Bug Fixes

* **代码评审:**  优化评审提示词 ([a0e243c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a0e243cc2bb1470f60375e5ef9880c7efad1575b))
* **代码评审:**  优化评审提示词 ([5b8ae7e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5b8ae7ed0802791560a091dc0ed900c4b7deae19))
* **chat:**  VUE 代码高亮bug修复 ([4ac4aa7](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4ac4aa77fb7a716362c8884321c53ddd68879753))
* **report:** 优化ctrlC操作只上报大模型回答节点 ([#55](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/55)) ([8870148](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8870148775e39ce66cbb114df2ebc901c76274e1))

### [1.18.1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.17.1...v1.18.1) (2024-07-03)


### Features

* **ai代码占比:** AI生成代码占比统计 ([41da4be](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/41da4be0f8911f8547d09d7acfc19f57759af98c))


### Bug Fixes

* **report:** 优化ctrlC操作只上报大模型回答节点 ([bba1dd3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/bba1dd34d16c225a06a2a381138392cc77459ac3))
* **report:** 创建新文件添加语言 ([e4e6326](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e4e6326123073600d603238c94c9388a7608da39))

## [1.18.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.17.1...v1.18.0) (2024-06-28)


### Features

* **ai代码占比:** AI生成代码占比统计 ([41da4be](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/41da4be0f8911f8547d09d7acfc19f57759af98c))

### [1.17.1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.17.0...v1.17.1) (2024-06-12)


### Bug Fixes

* **代码补全:**   修复不换行问题 ([6628ba4](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6628ba4f4f3f4c8ed2a6e2742be6cc764a37a8fb))
* **chat:**    修复图片预览失败的问题、预测补全添加函数&块级补全 ([e33956f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e33956f04eefc930a7e7e7defa4d686021d0dc2a))

## [1.17.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.16.0...v1.17.0) (2024-06-07)


### Features

* **代码预测:** 文件感知支持同文件夹和打开文件，支持批量翻译和代码评审，支持多模态 b3bf7ca
* **商业化:**    本地化和内部版兼容处理 1c997fc
* **商业化:** 商业化功能合并&登录首页样式更新 67299db
* **图片上传:**    解决图片上传和续写字符问题 0a91bb9
* **d2c:** 新增D2C自定义GPT 91d94d6


### Bug Fixes

* **商业化:** 本地化部署续写参数兼容 3b42347
* **商业化:** 本地化资源处理 b8b9b0c

## [1.16.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.15.1...v1.16.0) (2024-05-24)


### Features

* **代码评审:**  提交代码界面增加增量AI代码审核功能以及优化已知问题 8d1f4df
* **代码预测:** 代码补全续写增加跨文件感知能力等优化 29ad52d

### [1.15.1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.15.0...v1.15.1) (2024-05-15)


### Bug Fixes

* **代码评审:**  修复单文件代码评审bug 8bd5b93

## [1.15.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.13.0...v1.15.0) (2024-05-14)


### Features

* **代码评审:** 单文件代码审核 ([43cf12b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/43cf12b095ae4efa4f382415994a71f4ad2e1fed))
* **inlinechat:** 支持inlinechat ([7ff3161](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7ff31610bd0708d745f245558d0357b0e9dd2904))


### Bug Fixes

* **代码预测:**  解决函数闭合后还在预测问题 ([09b265d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/09b265d893e918202d1e02ba830f3e3552686fb0))
* **代码预测:** 根据上下文环境和光标位置优化续写触发逻辑，避免不必要的续写 ([11140be](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/11140be39fc40997e2c4cb3f730b373ecb466bbc))
* **message:** 兼容没有systemMessage的模型 ([055a32d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/055a32d13499579ae219fb8524ec8135469a705f))
* **pic2code:** 图片转代码使用gpt-4o模型 ([0ab4170](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0ab4170099f5653caf8ed488e11d111448a9c0b7))
* **sec:** 修复右键安全检查diff替换源代码位置错误等问题 ([c9fbf3a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c9fbf3a84a91b0cc6233c1643bc010b0e7169b80))

## [1.13.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.12.0...v1.13.0) (2024-04-24)


### Features

* **sec:** 支持内测白名单，优化限制次数为5、初始化代码仓库commit监听不全问题修复 ([43b46bd](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/43b46bd5e9869f6d4146b4151dd2fc61ea112530))
* **代码预测:**  代码预测优化，文生图表背景颜色修复 ([#19](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/19)) ([27bd789](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/27bd7893960af3bcce748533aac392cef1ec6809))
* **聊天窗:** 支持输入/呼起快捷指令 ([#21](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/21)) ([adbf89b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/adbf89bd5bfbe168afb3e550327fe168a7e16ce8))

## [1.12.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.11.3...v1.12.0) (2024-04-12)


### Features

* **code2flow:**  增加文生图表自定义GPT功能，可以通过文字描述生成时序图、流程图、脑图等类型图表 ([bb66586](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/bb66586a3b835af7fefb92b1981dfb8b3a2a632c))
* **sec:** 神医安全助手增量扫描添加开关... ([#18](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/18)) ([8d69524](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8d69524a232021cde24f7944adb8dba5ae8fd6b5))
* **代码预测:** 支持Tab和Enter接受补全、连续10次不采纳则关闭代码预测，避免用户打扰，修复已知问题 ([fc171e7](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fc171e7654491cabd29e54df7cb18eb95652ed97))


### Bug Fixes

* **code2flow:**  思维导图生成失败逻辑处理 ([#17](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/17)) ([8c47f8d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8c47f8d5ab7693fa450163f1c7b3a4aba9a4fd9f))

### [1.11.3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.11.2...v1.11.3) (2024-03-29)


### Bug Fixes

* **数据:** 补充聊天窗的模型上报 ([8cc5a6f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8cc5a6fa8dc708b93609bb0642d6f2d1354e6b1e))
* **数据:** 重复复制内容不做采纳上报 ([f5416b2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f5416b232e1e9abc7454ff14fbe6066b6ed8dbb2))
* **聊天窗:** 修复上下键可能意外锁定历史提示词的问题 ([b85b276](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b85b27618da4ce4233f816c8180490de9714dc6d))
* **聊天窗:** 统一处理远程配置，避免脚本片段的xss问题 ([60485e0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/60485e0e9023b6892c88deb4bac1c74593c82719))

### [1.11.2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.11.1...v1.11.2) (2024-03-22)


### Bug Fixes

* **代码预测:**  未操作过Tab采纳只提示一次 ，优化提示文案 ([74be066](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/74be06684a1124c7255e1231d4f9abdc40dc7b92))

### [1.11.1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.11.0...v1.11.1) (2024-03-22)

## [1.11.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.10.1...v1.11.0) (2024-03-18)


### Features

* **agent:** 自定义GPT新增个性助手应用(原个性化指令) ([f19d4cf](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f19d4cfeb145f54a60d0d4a34fca904630ede18a))
* **商业化:** 支持商业化赋能&内部体验优化 ([#7](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/7)) ([4919f73](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4919f730516e95ba73eb5e26eaebb54257ce885e))


### Bug Fixes

* **agent:** 优化提前中止输出体验&优化vscode升级指引 ([ae185a3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ae185a34e7ee86eab9a90a2fc334abaaaf7c6964))
* **agent:** 修复知识库中tool重复执行和语言不一致问题 ([31f72fe](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/31f72fe2adf18e3aaa3504fd9f239ec942927a9d))
* **agent:** 知识库类型缓存问题修复&优化agent区块样式 ([bcbcf60](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/bcbcf60fd63da573a59ca6f1f2e66bd06884d2e4))
* **agent:** 默认展开自定义GPT模块并支持缓存控制下次打开的开合状态 ([6b0f56f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6b0f56fb1fc53318f4b6c05a4fce34b8ee43bf56))
* **codesnippet:** 修复代码片段功能报错问题 ([220c492](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/220c49242fe2e824a2202812a92cc96f76ee7851))
* **docs:** 修改官网地址 ([34ccbc3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/34ccbc33665658df44c0439691798c1fcc186662))
* **login:** 增加退出登录入口&优化导航栏区域样式 ([ced2ca9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ced2ca98135322e527f0a8e900702b23dc29935a))
* **report:** 更换webmonitor接口 ([e507811](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e507811dc145ad63bdb09497908ad70ec4c3419d))

### [1.10.1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.10.0...v1.10.1) (2024-03-05)


### Bug Fixes

* **stylehelper:** 修复标签自动补全误打扰问题 ([8bf85a5](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8bf85a5a394e4b5e70e914cfc1b4c4e64020e763))

## [1.10.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.9.0...v1.10.0) (2024-03-01)


### Features

* **chatgpt:** 增加代码评审 ([6a0ff81](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6a0ff818ef8839d6029ca9fd8d8f8c27527e8160))
* **chatgpt:** 增加构建内测命令 ([c65bc64](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c65bc64003ea4c93cdfe422ebeef9a647aac27cb))


### Bug Fixes

* **chatgpt:** 修复webview中渲染html节点的问题 ([26417d8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/26417d86eacbe8a18253e7fb4639fab8d0baf708))
* **chatgpt:** 补充Agent相关上报 ([5a94ec5](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5a94ec5ecc926612d717358f828c6670998b9996))
* **gpt:** 调整左侧窗口Tab顺序&优化Tab在IDE放大后样式 ([b8b4420](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b8b44206372d994a45c482f21b2fae10046e8c17))
* **report:** 补充神医大模型自动修复场景和Cursor场景的数据上报 ([83e35a4](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/83e35a473a40e77b4c8d6792c8bfe3de7698387e))
* **snippet:** 优化Taro相关snippet ([510e691](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/510e69177235fd6d28fb4fb6d7caacbccfa94a55))
* **代码片段:** 支持在java,python,go,cpp中使用代码片段功能 ([933536c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/933536c7e2c1dc5b414b3ea819fc10e33ad61f4a))
* **代码预测:** 代码预测行数按照配置处理，默认1行 ([1584b27](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1584b2739003b92a8a8d7c296c9ab61dba8d9bdf))
* **内置浏览器:** 打开浏览器时增加loading以优化体验 ([e0970b7](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e0970b739159cd6956266d6fb9f2b8b72156a197))
* **悬浮提示:** 修复npm包含路径时的悬浮提示错误问题 ([186aef2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/186aef27f62ddd62a27863ce5681c7c545aeba16))
* **更名:** 快捷console增加不支持语言提示 ([a9412db](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a9412dba223a94e1c43d8acec5e1453a3843e9be))

## [1.9.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.8.8...v1.9.0) (2024-02-23)


### Features

* **agent:** 封装langchain基础类&联网搜索能力&网站爬虫 ([d0a5f82](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d0a5f822bf257352aad7b7964ba573d30278bd1a))
* **agent:** 支持公共知识库 ([b88eda0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b88eda08437bae8c04fe2edb280cf4b07ddef7fe))
* **agent:** 支持简单知识库RAG ([250035e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/250035e01f3b64de9adbec82fdc87aae8e95f4fe))


### Bug Fixes

* **agent:** 优化知识库的id读取 ([4ce15f9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4ce15f964875f510eedf88d9b1933454918cd857))
* **agent:** 低版vscode增加升级提示&部分代码优化 ([3ab2c97](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3ab2c97ce42c07fb9f784f8c6673958c79abe856))
* **agent:** 修复第一次提前终止后无法再终止问题 ([0510312](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0510312f674817fd9726f6172c71de5f73a2dd33))
* **agent:** 支持TavilyAPI搜索 ([b6cf385](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b6cf3856edcece28d8599b3ec5765bcd6039d749))
* **agent:** 联网搜索和网站爬虫增加beta角标 ([752927e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/752927e802373d62f5e49353fe5af477e51f751f))
* **chatgpt:** 默认关闭多轮会话开关 ([9e9437c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9e9437c40f029d0824a07ba2fc4755e404abbe70))
* **login:** 解决开启消息免打扰后点击登录无反应问题 ([4f6ac3c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4f6ac3cefd461d0701bd7fce0b43b90e6ebf4908))
* **userInfo:** userInfo支持写入userToken ([e771141](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e771141ebcad0360b3823775bcff982dc7818f78))

### [1.8.8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.8.7...v1.8.8) (2024-02-05)


### Bug Fixes

* **browserlite:** 低版提醒前置,防止不必要的puppeteer开销 ([664a0a8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/664a0a8626cde2585bc72ad41542c4cadf764a0e))

### [1.8.7](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.8.6...v1.8.7) (2024-02-05)


### Bug Fixes

* **autoupgrade:** 将支持安装的最低VSCode版本降为1.68.0,降低无法自动升级概率 ([21b82d9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/21b82d9630fd47b6532a2a3e969ccfa6169c040c))

### [1.8.6](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.8.5...v1.8.6) (2024-02-01)


### Bug Fixes

* **report:** 修复代码补全不采纳的上报口径 ([5fd6493](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5fd649386f10e946f1a579bca274a06c2e4ac343))

### [1.8.5](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.8.4...v1.8.5) (2024-02-01)


### Bug Fixes

* **chatgpt:** 修复部分设置不生效的问题 ([55c0cca](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/55c0ccae90e9675102f38a91497792ea711d1f3d))
* **sec:** 修复神医大模型数据上报 ([476624c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/476624c18055e035300a510ff582fdfd10e2990c))

### [1.8.4](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.8.3...v1.8.4) (2024-01-31)


### Bug Fixes

* **login:** 优化getJdhLoginInfo的类型 ([e4e2481](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e4e2481fedb961ae67f3b4b72f3d506dedd441a2))

### [1.8.3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.8.2...v1.8.3) (2024-01-26)


### Bug Fixes

* **chatgpt:** 数据上报&帮写文档 ([c5dbc1a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c5dbc1a023e9099f3833e98969832de50b467f70))
* **sec:** 右键安全检查生成的代码，不再进行安全检测和修复... ([#61](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/61)) ([61cdf3f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/61cdf3fbef5d28b19c875ea2fa983bbb686e2db4))

### [1.8.2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.8.1...v1.8.2) (2024-01-15)


### Bug Fixes

* **chatgpt:** 补全上报代码行数&修复聊天窗主题联动&初始化体验优化 ([c7842f7](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c7842f71c0148c99b518679a9aba48e1f4889ffc))

### [1.8.1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.8.0...v1.8.1) (2024-01-05)
### Features

* **聊天框:** 支持左右配置 ([294df61](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/294df61dbe69999d16b429a1d8f66374b42381de))
* **代码预测:** 模型配置切换 ([2bf200b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/2bf200bb1c51b4a6c84588a3e51645cce962cd42))


### Bug Fixes

* **chatgpt:** 修复存在2个空节点时，react报key重复问题([6466dc](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6466dcce4e69d1a820a5b26f1c797f396b41f3fe4))
* **codesnippet:** 修复配置snippet中存在script标签时影响webview展示问题([6f9da14](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6f9da14a6458938e25116ce1d4aa9a6deadfcdfd))


## [1.8.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.7.1...v1.8.0) (2024-01-02)


### Features

* **前端标准体系:** 新增前端标准体系相关文档&增加can i use跳转逻辑 ([c7009b0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c7009b0409ad5828838a4d1808c994d0e38a3803))
* **chatgpt:** 聊天窗返回代码自动进行神医大模型安全检测 ([2bf200b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/2bf200bb1c51b4a6c84588a3e51645cce962cd42))


### Bug Fixes

* **风险提示:** 强调聊天窗输入内容合规及隐私安全问题 ([e46bcaf](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e46bcaf93fa453df2b418ed16045a13509a25401))
* **聊天窗:** 聊天窗增加erp水印 ([6f9da14](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6f9da14a6458938e25116ce1d4aa9a6deadfcdfd))
* **聊天窗:** 聊天窗erp水印颜色变浅 ([e8b4aba](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e8b4abaf16f9c616e1cbb1c0da7f7dc03d684ad4))
* **autoimport:** 解决autoimport冗余执行导致的babel/parse报错问题 ([84b164f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/84b164f19ad06d1a739a7a04eb17b24dced9d6a5))
* **badjs:** 修复badjs查询工具 ([5bd9b98](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5bd9b989a78e4e1b350af96092216a191c72c3d6))
* **git:** git操作查看文件支持查看当前分支 ([fba136e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fba136e3375c6ec9ff73f54f19a0b2867ff50b8d))
* **html2css:** 优化html转css菜单文案及展示条件 ([f03a694](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f03a6940c1f7fe49158ee382f2c97ecbefb8b92b))
* **menu:** 优化右击菜单层级&下线条件编译功能 ([deb7c62](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/deb7c6280b74949dd5acf09ec7428752710514bb))
* **sec:** 右键安全检查传当前文件类型... ([#46](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/46)) ([b7ca151](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b7ca151db24e5be7c5d131ccfc0173a27a177716))

### [1.8.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.7.1...v1.8.0)(2024-01-02)


### Bug Fixes

* **聊天窗:**  优化聊天窗高度调整体验 ([b2093a88](http://xingyun.jd.com/codingRoot/JoyCoder/JoyCoder-VSCode/commit/b2093a88c6f64ae0389dc4a3ad71bcf90ecf631b))
* **autoImport:**  解决autoImport冗余执行导致的babel/parse报错问题 ([84b164f1](http://xingyun.jd.com/codingRoot/JoyCoder/JoyCoder-VSCode/commit/84b164f19ad06d1a739a7a04eb17b24dced9d6a5))

### Features

* **代码预测:** 优化代码预测补全返回速度和准确度,优化行间菜单支持更多语言 ([a8c911e](http://xingyun.jd.com/codingRoot/JoyCoder/JoyCoder-VSCode/commit/a8c911e8ca456d09669dedda442bd52a39d033b9))
* **SEC:** 聊天窗返回代码自动进行神医大模型安全检测 ([40b651a7](http://xingyun.jd.com/codingRoot/JoyCoder/JoyCoder-VSCode/commit/40b651a7a6b099084ba415b442b5d50e2834e36e)
* **增加左侧聊天窗:**  新增左侧聊天窗口&导航栏，支持相互切换 ([22a7c98f](http://xingyun.jd.com/codingRoot/JoyCoder/JoyCoder-VSCode/commit/22a7c98fb5460b8dac43e213c73b0e2bb41adb16))


### [1.7.1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.7.0...v1.7.1) (2023-12-20)


### Bug Fixes

* **completion:** 上报补全&取消判断优化&合并请求方式... ([#47](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/47)) ([2a75dff](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/2a75dff38befe7a6a9cd3849a3fa9ee4e050e6cc))



## [1.7.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.6.0...v1.7.0) (2023-12-12)


### Features

* **pic2code:** 优化图片转代码体验(使用高分辨率模式+图片使用占位图) ([a7f1f50](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a7f1f50de51bad4c37e2db20f0d4d501b342394c))
* **sec:** 增加右键神医安全检查功能 ([44fbe80](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/44fbe80f60634e587a80282a41b6b5907772500e))


### Bug Fixes

* **插件包:** 清空插件包,以免这些插件跟着fe被卸载或禁用 ([65cb220](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/65cb220d6d387758ea3d33707429dae514847b76))
* **预测:** 行间菜单函数注释优化&code llama上下文优化 ([e9c4753](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e9c4753500701d4519534353992bd31b5e7c0e60))
* **gpt:** 解决聊天窗缩放大于1时无法滚动最底部问题 ([476b11c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/476b11c978d3c61b2785b00efdfb1200759961db))
* **login:** 补充登录完后回跳VSCode逻辑,顺便带回SSO登录信息 ([74d0a0b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/74d0a0bcda6e986c6a07818aa472f4ca8d5a147b))
* **login:** 登录统一使用健康登录态,以解决部分用户登录失败问题 ([aa749ea](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/aa749ea40c27d22a2c408a6788a89b0bcb15fff2))
* **upgrade:** 支持配置允许使用的最低插件版本,低于该版本则引导升级插件或vscode ([12115ed](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/12115edf4904b0b89d0864e8f394101d903d4a97))

## [1.6.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.5.0...v1.6.0) (2023-12-04)


### Features

* **代码预测:** 控制调用频率，减少模型压力，解决部分bug；优化行间菜单体验，代码注释改为在当前代码块中添加生成的注释 ([053b935](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/053b93553392f9fab9ad6ccc328cfb7dc57693fb))
* **marketplace:** 市场支持推荐和批量安装... ([#34](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/34)) ([8fa729d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8fa729dfd9374dcbd56610fb55174b3430b02db5))
* **pic2code:** 图片转代码支持选择UI框架&优化系统指令以得到更精确结果 ([d297682](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d2976822f5874025937b765e4b283760789cf0ca))


### Bug Fixes

* **登录:** 修复少量用户登录提示成功后仍需登录问题 ([c45fc55](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c45fc557a7389f09f6bbaebed4ed93872d45ce03))
* **ai:** 模型的通道控制由接口下发 ([21eed9d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/21eed9d83292b1729cf1bfdb418bfb01a5ff3d5b))
* **upgrade:** 强制切换至新官方工作区 ([d3d9f8d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d3d9f8da47b8fa56f9e81b73506acd9c8ee76aa0))

## [1.5.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.4.0...v1.5.0) (2023-11-27)


### Features

* **代码预测:** 函数代码块增加顶部行间菜单&代码预测模型配置优化&状态栏优化 ([ac8c998](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ac8c998c992f149590f0736b915a41aabd3f9c71))
* **browser:** 增加右击html文件可打开预览调试功能 ([a42b82f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a42b82f74c95a52bb583c2bd705888c0caee0f37))


### Bug Fixes

* **代码预测:**   修改logo命名，避免发布报错 ([934430f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/934430fcf849ae0585b8e9633395d15b94814248))
* **代码预测:** 解决部分问题 ([67ca60b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/67ca60bdce4e2b63239db9ec1b9dd7a669cafe25))
* **登录:** 默认使用联合登录 ([b2f0530](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b2f0530df933505c77c8517347b268387644eb7c))
* **会话导出:** 修复导出会话时获取tab标题报错问题 ([#22](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/22)) ([d0c7494](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d0c74940e9d198918c38a82b8aa36bea3151be08))
* **chatgpt:** 数据双上报，优化GPT4调用 ([4759b6b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4759b6b79bb0cb006f17671a46d2a609f10ea0bd))
* **GPT:** 修复普通问题序列化后多了双引号问题 ([dbf7fe1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/dbf7fe181b5785352933eb7d042bacef787e932b))
* **GPT:** 优化图片转代码相关代码 ([f4dd414](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f4dd414705075a9dfea1249ba9d05feb5cdf0ee8))
* **login&pic2code:** 增加通用的md中插入按钮方法&完善登录以及图片转代码体验 ([56f4ac9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/56f4ac99892929b5b4835c825333e414f2df9f9e))

## [1.4.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.3.0...v1.4.0) (2023-11-21)


### Features

* **代码预测:** 代码预测状态栏图标和位置优化、支持多模型切换、预测代码格式化、yapi生成JS请求代码 ([def9126](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/def9126e256b2bc1b7f6fda9e17ac2ed89ec739f))
* **pic2code:** 增加UI草图转代码功能 ([a769a83](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a769a8326239c8383c7256ea59ce3717abdba38d))


### Bug Fixes

* **pic2code:** 图片按原图大小展示&增加复制代码&预览快捷操作 ([07de078](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/07de0787f3352b7a101439ad358f1c445c35d52b))

## [1.3.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.2.0...v1.3.0) (2023-11-10)


### Features

* **会话tab:** 一个tab最多保留最近10个会话id ([6d9e0ce](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6d9e0cea40f80ca0f436918e7fa37d543de82693))
* **内置浏览器:** 支持调试功能 ([f167d85](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f167d85662b4e07c9bf3cc92417040d1e9847b9e))


### Bug Fixes

* **版本升级:** 更新升级文案 ([77558e6](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/77558e6185682606378aa31c0aa9dc87f1b29886))
* **版本升级:** 修复同步hibox配置时报错问题 ([d04a87c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d04a87cb8bcecfa32d35239e0860bf0f69ea10d2))
* **代码片段:** 优化taro代码片段 ([3783145](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3783145dc1172d0a1c1fa2dfdbb3498eb51ad01f))
* **问题反馈:** 增加问题反馈入口 ([66f7348](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/66f73489d6c8216c04bc7eab5eb52d03da47497b))
* **gpt:** 修复scrolltoview+fixed导致的页面抖动问题&优化底部样式 ([a308727](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a30872729544f53a13f6d28f8be9384f7d96b512))
* **gpt:** 支持星火模型文生图 ([79ebe91](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/79ebe91088f1945077cd5c7b0da2a2eb07877d04))

## [1.2.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.1.0...v1.2.0) (2023-11-04)


### Features

* **代码预测:** 增加代码预测补全功能 ([81e9b82](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/81e9b82addaf7ec0dc14ccbe4d1002e52a9e9db3))
* **会话导出:** 支持会话导出md文件&tab切换兜底逻辑 ([eb7e867](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/eb7e867c78cff158ae4c7bb433a3a25523ac815d))
* **会话tab:** 支持tab修改别名 ([f33fe13](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f33fe13cd5487484bf05edc7deaace3b4d3ede77))
* **内置浏览器:** 支持复制粘贴剪切事件 ([ca0b72c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ca0b72c7828321b4a9e8079e16fadbc233126541))
* **内置浏览器:** 支持设置代理 ([b3a7219](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b3a7219baf75400f3f5f710b1ffcb94e389c540c))
* **内置浏览器:** 支持使用默认浏览器打开网页 ([4e4da01](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4e4da014c96dc1606c25eea38882c4fb80a915bb))
* **AI助手:** 多会话tab交互完善 ([0475b08](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0475b087f9f0de618c9e29be8c67f412bb0ce853))
* **ChatGPT:** 支持多会话tab ([4c00568](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4c00568bc0117abedc5650db13d596e62bff7d4b))
* **chatgpt:** 主题按钮迁移 ([9d62573](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9d62573ef5b2552819f1b08bffa55a28b05ed7cb))
* **Taro:** 路由自动补全 ([b48f24d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b48f24d03fbc17008345425afb7f38d1e6be25a6))
* **Taro:** 增加悬浮React Hooks时提示官文档功能 ([e0ab64a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e0ab64a69c0de007241246d73979010888355645))
* **Taro:** 增加悬浮Taro组件,Hooks,API时提示官方文档功能 ([c86d1e0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c86d1e0d89d4047fab34e230c3d50e7228c58738))
* **Taro:** cmd+点击路由可跳转到对应页面源码文件 ([46d188e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/46d188e53c3f364680a655d1ffc7877108262ad6))


### Bug Fixes

* **版本更新:** 增加手动更新功能&优化更新提示体验 ([1301c03](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1301c0316a1bb826afd922c5596e0c062613afbb))
* **会话tab:** 初始化模型渲染问题修复 ([29efc40](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/29efc4020119eef39778c7cf360b2e8ea12d80eb))
* **会话tab:** 更新模型时同步到defaultQaList ([253011b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/253011b54579e095c2a30d077ea62ed4836dcc20))
* **会话tab和导出:** 体验问题优化 ([84c9aea](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/84c9aeab6391466ea69592791ba720d2e91d0ad1))
* **会话tab:** 模型切换后头像需要更新 ([c13e2a8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c13e2a8fc53b280169a8b578c2687a6160aa1112))
* **会话tab:** 默认序号问题修复 ([6ae7da3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6ae7da3b1728f9a3bf750d42292265c2f42e91d8))
* **内置浏览器:** 解决开多个tab场景下的各种问题,如跳转target=_blank ([3bb1c57](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3bb1c5748e67314c71ea50548591dc9c36ee5640))
* **内置浏览器:** 限定标题最大长度 ([b84ca4c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b84ca4cc695f295ba3a621ac390492a5d3bfd853))
* **内置浏览器:** 修复部分屏幕鼠标事件错位问题 ([4f35aee](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4f35aee85224e443f961141aed0c920d8dd132b3))
* **内置浏览器:** 右击不需要展示复制粘贴菜单 ([e0bc1ad](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e0bc1ad1108519d3ddf9f8189cf120b615e4e3d2))
* **悬浮提示:** 优化NPM包悬浮提示体验 ([d66a202](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d66a202ecb5ebeb9ccabe9de7f7b868ea0e4c6f7))
* **Taro:** 优化taro代码片段 ([b9c0d5b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b9c0d5bd1dcfe2b4ff09deac08c90ce39ef58d7d))

## [1.1.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.0.4...v1.1.0) (2023-10-24)


### Features

* **快速搜索:** 结合内置浏览器新增文档快速搜索功能 ([58e921f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/58e921f829c25ef63bf8284603616276559229bc))
* **chatgpt:** 新增清除会话功能 & 优化设置按钮样式及交互 ([40516a7](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/40516a749b0872fb6007205ed0bfb172e91ecf59))
* **chatgpt:** 右键菜单增加代码翻译功能 ([d4060e7](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d4060e7e48d24f93bdc477e9e50fc6695c1232b8))
* **chatgpt:** 支持更多模型，开关多轮对话，输入框性能优化 ([79f6a14](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/79f6a147065532d10fc733fa0563c2b4b99875fe))
* **chatgpt:** 支持更多模型，开关多轮对话，输入框性能优化 ([c7a5cb0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c7a5cb00dfa1e908f082b403fa56b5d24d9e35ae))
* **H5调试:** 初始化内置H5开发调试模块入口 ([b321835](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b3218359b418ad784dda1eb3af2c249a6a07b2cc))
* **H5调试:** 删除冗余代码 ([642daf6](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/642daf69c31e5ed21757660612b18e89a66e9b52))
* **H5调试:** 完善调试映射页面 ([d0c9cff](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d0c9cff67b39f7038a1f50c5ad8f54e5dd18fe44))
* **H5调试:** 优化视图样式布局 ([f09bae8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f09bae885887086922cc5d9b9e1164d0dea21c33))
* **H5调试:** 增加调试映射页面 ([cf4246f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/cf4246f8b36603fe1673901d6e62735ebc094d02))
* **H5调试:** 增加视图缩放逻辑 ([4c58ae7](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4c58ae77f9023aaf51f16967bebc23fd8df2beed))
* **H5调试:** 增加视图缩放最大尺寸限制逻辑 ([4428430](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4428430345f8dd173e6355f9706771a03c5d93cd))
* **H5调试:** 增加无头浏览器逻辑 ([6b9eab5](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6b9eab53eed07baf1fd0d287d01640da21bdf26d))


### Bug Fixes

* **菜单:** 优化编辑器菜单顺序 ([331d41f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/331d41f0f0cb7d50fec2b6e07cd814c527328221))
* **快速搜索:** 解决本地调试时由于webpack缓存导致的模块引入失败问题 ([56f4e88](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/56f4e88b035f8df0926fa1b6dbbd1a05afb29075))
* **快速搜索:** 快速搜索增加百度google&浏览器默认语言设置为简体中文 ([a5fac61](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a5fac61be30efb91b7128c4dc7cad01728ef448d))
* **快速搜索:** 文档辅助体验优化 ([60e7822](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/60e782229d3f7e8a76a5885c30d87a7ca097f8ce))
* **browse:** 内置浏览器自测优化 ([2222c76](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/2222c76dd134f8c460b8f5a70b2a6f1c304d3558))
* **browse:** 修复放大vscode后浏览器页面展示模糊问题及尺寸过大问题 ([0a80788](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0a80788e6863f7648d2539ade694fdf19f4a54fd))
* **browse:** 修复无文件打开时分栏问题 ([ef97ab4](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ef97ab4232582077ee039a1b0266163b0402ce76))
* **browse:** 修复badjs及打包后样式问题 ([7ae1868](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7ae1868947a6e6ca896425b516c43bb2336cb176))
* **chatgpt:** 默认的systemMessage修改，支持model入参 ([0945f1d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0945f1d96a9bc83c25da3fe765d23144d7cc71fd))
* **chatgpt:** 默认的systemMessage修改，支持model入参 ([a7a6e3d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a7a6e3ddf3e90618fe5d3da87cc7d01a3a6ed454))
* **chatgpt:** 修复第一次展示模型未更新的问题 ([d16c39f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d16c39ffebaedfbf41b42c2e0a2b0962f59d87d3))
* **chatgpt:** 优化多轮对话模式切换的体验 ([fa0b21c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fa0b21c9c7a5a641618a3e3a06e6ff43bedd4ca6))
* **chatgpt:** 支持读取远程的模型icon ([8879ef5](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8879ef563752f0876aa3aeb7560e98d039421326))
* **chatgpt:** 支持读取远程的模型icon ([34f0bf9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/34f0bf981c3b31bb578114d68dbcb6d7d190e33b))
* **doc:** 补充常见问题和加群跳转链接 ([31983ff](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/31983ff7868be230d901ba2e96ebb22030f1e219))
* **H5调试:** 修复模拟设备尺寸异常问题 ([6f0092b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6f0092b7a422c66f59e51f3146fcf78802bff240))

### [1.0.4](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.0.3...v1.0.4) (2023-10-16)


### Bug Fixes

* **text:** 错别字和文案调整 ([b9a8fed](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b9a8fed6450e698df44342c422676369f2f5497b))

### [1.0.3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.0.2...v1.0.3) (2023-10-13)


### Bug Fixes

* **codeshow:** 1024节引导语，补充监控，必须输入10行代码参加 ([9093cc8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9093cc82a95fa26bbedbbbcb6f138b4320d3d68c))

### [1.0.2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.0.1...v1.0.2) (2023-10-13)


### Bug Fixes

* **codeshow:** 1024节引导语，必须输入10行代码参加 ([935bf7b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/935bf7b3607790e02a6fb9956bf6b3534ff985ea))

### [1.0.1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.0.0...v1.0.1) (2023-10-13)


### Bug Fixes

* **codeshow:** 替换为线上的打卡接口 ([0d9d851](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0d9d851c2ee1a346a9a72662eb6c22ab874b7eeb))
* **codeshow:** 补充1024节打卡描述哦 ([6c8e852](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6c8e852797f9ab1fc2ae0964e965838f9971e903))
* **codeshow:** 补充开关，修复添加打卡内容时，主动关掉弹窗 ([82ade60](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/82ade60f5fcdf84ded547195a772197767f4cbeb))

## 1.0.0 (2023-10-13)


### Features

*  优化远程命令行解析方式，调整顺序 ([a27de21](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a27de21f5d81d5149362b9bd6746c5c420b227bf))
*  增加chagpt ([e9dfaa2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e9dfaa2c184d1acde8c9442cc5db546baaf8feb1))
*  chatgpt异常交互提醒，去除日志提升性能 ([c815a0c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c815a0c31d89706369bc007d21888f6e28eea2f0))
* **插件包:** 下线插件包特性,只在使用文档推荐,不做强制安装 ([acaa587](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/acaa5873ee9cc0f081a0cfd1ceef9b674a76a21a))
* 插件配置数据切换至水滴数据源 ([8cbe2ed](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8cbe2ed418377edaab7e30d4436c4d8822483ec2))
* **代码重复检查:** 查重参数调整 ([5b98ada](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5b98ada06e84312431a0d6812bec683aa6c613df))
* **代码重复检查:** 代码路径调整 ([66cc5b2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/66cc5b2bcf27e329c6d5bcc7833608c26c70ebae))
* **代码重复检查:** 提示文案修改为中文 ([7842c25](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7842c25b03772f11bddc48caed4c52a9e59b3449))
* 代码重复检查支持选中多文件 ([e7b20e5](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e7b20e51f284e6a38698caa2023cd14d58dd3457))
* 调整打开ViewLoader里的viewColumn配置判断逻辑。 ([b2ce21a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b2ce21a137c5776ec76b4370d7b3a1dd78bbda4d))
* 更新自定义编译 ([8f36412](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8f364125ddfe670dd83423aca52665338d8d3f9e))
* 工作目录支持相对、绝对、项目根目录 ([761a942](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/761a94219d83e1af27c6941d767ac278aade489f))
* 恢复插件包功能,只附带安装轻量且较通用的插件 ([5c97b38](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5c97b38db914c7e2cf65b7c0f9a41119bea36304))
* **快速创建:** 处理写入路径时减少变动。 ([620b1f8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/620b1f8c85e17b7565d64eae47575fe72a9497b7))
* **快速创建:** 创建时替换文本。 ([29ff828](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/29ff82849e4d012224df9e7fd5d9d0674b2c28ba))
* **快速创建:** 埋点。 ([6ef8976](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6ef8976787348e16f1244209a5007960f19318d1))
* **快速创建:** 模板列表样式调整。 ([4f54e3f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4f54e3fbb4fd4c4fb130abb47d51fed7c8957246))
* **快速创建:** 新增页面增加路径写入。 ([3fa36f1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3fa36f15132f4da7ba1e6a70274d1acf42e0ad90))
* 快速开始支持第三方仓库及部分代码重构 ([f15e0d0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f15e0d09b6fc6b7e11b8ddc9c9140a3a0812e373))
* **快速开始:** 支持Taro项目/页面/页面组件 ([0651860](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0651860268eefbbbd8952b0bce6511b436e64803))
* 命令行工厂重构，支持远程配置化 ([ca18ae0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ca18ae03217389b357eca92ad51abe6fa7f36a85))
* 命令行执行拆分本地&远程，修复beta发布问题 ([fac16fc](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fac16fc25e214fca6661c9c3d9f4212e1a9d0686))
* **品牌融合:** 插件更名为JoyCoder-前端专业版 & 无用功能删减 & 结构及文档优化 ([cee2baf](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/cee2bafc26e1eb9aa8fc48a99fb27b388c30faf6))
* **品牌融合:** 首次安装时自动同步HiBox配置并卸载老插件 ([c1d448a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c1d448ac5f4310a882e69e6dd929436203f09a50))
* **品牌融合:** 优化新老插件切换体验 ([b50ec93](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b50ec932b0c2efab4f63bb1cad630fcbb04361e7))
* 使用vscode插件脚手架生成插件代码 ([a970c68](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a970c68ff22d65c1e1e624ea7f373e07a0cebd2c))
* 树视图增加问题反馈等入口 ([8904efc](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8904efca3b329f58753f938b4c45b3d463ea9fcf))
* 搜索增加快捷键 ([11ab12e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/11ab12ed849fe5a16d7bf7a175abd6c983d90fb5))
* 条件编译自动编码初步 ([0a97926](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0a979264d2c6939c5c351e1d4cde70ece9e815d0))
* 统一message、清理多余console。 ([f119cc8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f119cc856275d11929b0c4426fa1c931ac7eaea5))
* 物料导入体验优化 ([3a8d8e4](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3a8d8e4235347012fb85ce75935d8d1128ea49ce))
* **物料导入:** 支持配置二级Tab及版本选择 ([8d21838](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8d21838e4bdac83bd50a2a1d47159ca9453ec437))
* 新增代码重复检查能力 ([e7c4a02](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e7c4a021cfb3e601787217cdb02e340a847dd105))
* 新增快速创建插件。 ([fbb8865](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fbb886565b7de50dd9474a208ad7ba48179dd9e6))
* 新增快速切换工作区&主接口可用率上报等 ([0232d41](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0232d41e0f787c466317f150268b1bf854ca89dd))
* 新增快速搜索(组件文档、接口查询、kibana 查询) ([#1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/1)) ([a2aaa85](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a2aaa85ded827f0636b6ba68461b5b842711723c))
* 新增快速添加console代码功能 ([b090feb](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b090feb27c4d1b4a61a833d011fb38e26c5d978d))
* 新增上传剪切板中截图特性 ([fa3dd8e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fa3dd8e10af8db294a2e91baaf8bad9541e1c8ad))
* 新增推荐插件 ([7572b11](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7572b11c4f5b9a3ebf3cf0a81468ff4b725f7408))
* 新增无用export代码扫描和无用文件扫描功能 ([4beae97](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4beae9701990635d971de61533f5bef118b04845))
* 新增物料导入能力 ([ce11403](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ce1140381f78aebf00591521dff0343696edbc4f))
* 新增悬浮ip时直达webadmin能力 ([cbae565](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/cbae565372f34eb53a11b35db1f6d5e75ec6e60e))
* 新增右击页面目录查看该页面badjs功能 ([7c0ed78](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7c0ed789c141ca99739900f9f372c05e40a4ed7e))
* 新增自定义配置命令 ([fdaf654](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fdaf654d6bc85018b222936ff9ebda32c83b0f4b))
* 新增git辅助功能 ([74b9c8d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/74b9c8d9f0d01013aa272f457622a3feff5d4603))
* 新增HTML转CSS/SCSS/LESS功能 ([a7a6bf2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a7a6bf22d08f2f2a3c329238a1f03eb487715785))
* 新增xbox日志输出渠道 ([846a5d7](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/846a5d7e0c88d0d3db44dcbe8c375e4e00939abd))
* 优化标签自动补全,并在补全时自动添加css代码 ([c1c26c0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c1c26c0fbde2fc202b3f85f34952d7ae54c611dd))
* 增加标签补全为cssmodule能力 ([e7cdcb3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e7cdcb33f940a6c84215452a9fccec30663a30e1))
* 增加侧边栏入口 ([c14e95c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c14e95c98e220d113efeae023b7a1a9a8be125d0))
* 增加插件包能力,安装hibox时自动安装其他推荐插件 ([c934456](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c93445646c1ef86284c4136eb1824ef3588915b7))
* 增加公共跳转方法 ([30cc0d0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/30cc0d08c139a0ff2f68aea84d66a1d91c96ceea))
* 增加公共webview ([f6ebab9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f6ebab908df992dd7cf1a3c6e5c4af04a78f2ad6))
* 增加快捷操作树视图 ([2041281](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/20412813ae8a26ab8bdb18ee89f5e11a385e7b72))
* 增加快捷导航树视图 ([68e23cc](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/68e23cc25a8c66c73accf43d236709bc2fa2bcaf))
* 增加数据上报 ([6372c11](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6372c11faa6b90da7d02c94ebd8dea7084e058cb))
* 增加图片上传功能 ([7702dea](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7702deae4f4255653d97cba8fb6e45b77d529c4c))
* 增加悬浮提示能力 ([28e89f9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/28e89f9db11775daebff6e13efb9b42d35d56a95))
* 增加远程配置获取及版本更新提示能力 ([9a2975e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9a2975ed6295e528269f17ea4751ff7fac11bcb2))
* 增加chatgpt快捷入口 ([a7aed03](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a7aed03fc46e445cc28d7f27ec207e7d1de6a455))
* 增加css 定义功能(css预览及跳转定位) ([24e430b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/24e430b3d745c5d55fe868e69c7d2871e43a9389))
* 增加css转为cssmodule能力 ([d8bcf44](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d8bcf4409411ca0332c69f120111d325666f2681))
* 增加git发布管理后台功能 ([4139cc1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4139cc180d117a63e8bac6f46293e047196871c6))
* 增加import代码补全 ([b7f7315](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b7f7315d9159012824565f946856ed2233713026))
* 增加snippet代码片段能力 ([3c22582](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3c22582292673acb35e979a9e15dc14d09a5b87c))
* 支持插件市场一键安装 ([67ba988](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/67ba9880d448d448af0e09b753ada4828ad0bf00))
* 支持插件自动安装更新 ([f176bf5](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f176bf50acd1bec36149ee6e58b327a920743cc1))
* 支持远程配置来开关非通用性的功能 ([833249a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/833249a237f495afc402814f236f5528f763fa5f))
* 支持cursor类似的交互能力 ([ca6335c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ca6335c9006801d65f9185a2d0a42b9616f95d0d))
* 支持ERP登录态打通 ([01868a0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/01868a0a30bcb16df04f05c138df450071a908ec))
* 自定义命令编译支持多选 ([d9844e8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d9844e800c729cb8d1e2aeff9dddce0777489a44))
* chatgpt代码块增加插入和复制操作 ([3599ddd](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3599ddd9667117c49f17ad9e13356c8379757b12))
* **chatgpt:** 频限支持GPT4模型 ([7be03be](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7be03bee2c20ce1b9e57e8be95f66e1b081efa3b))
* **chatgpt:** 切换到远程代理服务 ([b39aec6](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b39aec60eea4f25cfccc118636114d990a87a1be))
* **chatgpt:** 通过你好判断登录态和GPT服务可用性 ([c37a49b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c37a49bb60cbff5f37c5017c133f87d564ed5943))
* **chatgpt:** 新增个性化指令特性，配置项UI交互优化 ([edac604](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/edac604754105a364b98a15d4711d8baa7e07e72))
* **chatgpt:** 优化你好的登录体验 ([0357d4c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0357d4c6ec4318f2d146705b9636e50d77c0ce44))
* chatgpt增加提前停止功能，支持配置私有key ([e5d769f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e5d769f8841ac50bdc57202d7cfada9be126a61e))
* **chatgpt:** 支持调用集团ChatGPT网关 ([b6f4ed3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b6f4ed313cb2fe7b3a253c21a2f64fa0485795ad))
* **chatgpt:** 支持复制和重试功能 ([d3e03c8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d3e03c8c08b46905443c1ccff06dc088836729c3))
* **chatgpt:** 支持私域向量知识库 ([acdbe7e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/acdbe7e66972796a1e6b3466f9f1bb3f825b101b))
* **chatgpt:** 支持提示词收藏&快速检索历史提示词 ([9a054e7](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9a054e781af8bf29c734a4f49509cc375ac745cb))
* **chatgpt:** 支持用户选择不同模型 ([c108417](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c108417230f5d71224b8a273a7336ca69b43a1eb))
* **chatgpt:** 支持insiders版本vscode登录 ([857156a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/857156a34af1a2ab8dc95e5c12c9d76f382053eb))
* **chatgpt:** chatgpt服务切换为java版本 ([10f541f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/10f541f11acb7da6f442d52662a810f909c9c492))
* **cli&quickstart:** 适配Leo：支持Leo快捷命令行和常用Leo模板快速创建 ([#34](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/34)) ([23a5078](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/23a50787ea63322d9ff019e82cc6f737f76b88e1))
* **cli:** 编译taro京喜小程序，判断wxapp是否有编译产物来调整timeout ([5e21ac9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5e21ac9c14d56bbcfff862875d2980de0af7caf1))
* **cli:** 代码优化，命令长度为0 的异常流判断统一处理 ([3b3fb69](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3b3fb6954cec7313d75f35fd53f5ffee7b8c4dfb))
* **cli:** 递归往父级查找目标文件的逻辑收敛 ([512f010](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/512f0109a30ba0d9614a884ce7403923ab3d2e00))
* **cli:** 发布beta包时远程版本选择beta和latest中的较大值 ([78c3ad9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/78c3ad9725f77bdd59f0d43f053f5e7b3861e222))
* **cli:** 根目录提示文案优化 ([52e6c36](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/52e6c365eef096fe652039acf2d29ec47ebd8f74))
* **cli:** 更新beta的基线改为npm verisons中最后1个 ([13ce290](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/13ce29034a2c198e4094f5aef924a007d8d7b300))
* **cli:** 关闭命令行后commandBar隐藏 ([4b2e859](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4b2e85958dd181751a0a64496acaab67f011a623))
* **cli:** 使用shared/logger标准化展示消息 ([5d12b65](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5d12b653682743af2e8190b725b083f3526ac872))
* **cli:** 提示文案调整 ([fc36aa1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fc36aa179086f360aaf89d210386812ab919e501))
* **cli:** 文件窗口内右键也展示快捷命令行 ([10d880d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/10d880d02009f7f5f2f26e477555123854822a3b))
* **cli:** 增加不限制目录的legos命令 ([133012e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/133012e28a3aabdcbb52442b373a2a144f74a2d3))
* **cli:** 增加关闭所有命令行 ([4e19318](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4e193189c2963f15a527e53587c9bf3258ec53cb))
* **cli:** 增加使用次数上报 ([dd158f7](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/dd158f78c6e2c080438f32b1c1f488a478672cca))
* **cli:** 支持任意仓库发布beta版npm包 ([05b7b07](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/05b7b074f4733e53225776cea56c02fbc175d035))
* **cli:** 支持jxapp单页发布&基础库发beta版本 ([3f4039f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3f4039fd6a3892bca6e74453ba46c4997cf226f7))
* **cli:** 自定义配置命令行统一执行器 ([f3450eb](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f3450ebb22b5a448a7c135aaef5fef9407921f7a))
* **cli:** jxapp项目H5单页开发能力 ([6494b81](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6494b81521b494dffcd6eec9b71e3bb182a54cf4))
* **codeshow:** 提示词模板支持可配置 ([517ddb1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/517ddb1daa34eb9ca1f0af8e5e158b4f5d02a408))
* **codeshow:** 替换为京东红色系 ([ed109b9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ed109b9ba0af131302a7342e4094f8b11b8246e0))
* **codeshow:** 语言读取优化、可配置化活动 ([10b193b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/10b193bbd8ad7bb21c6d6503a58b338b1a062563))
* css中插入图片时增加宽高信息 ([acda4cf](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/acda4cf4045cefcdd2d046dcab62c4a0fcbdcb9d))
* **icon:** 侧边图标替换为outline形式 ([062e663](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/062e663525b83a83184a4aed991035d32bd4ed92))
* **shendeng:** 支持1024神灯姐打卡活动 ([48adef2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/48adef2fffd7615f7041c9c8bec2a286310e649a))
* snippet支持远程及本地配置 ([32f07ae](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/32f07aee2333c679d49c0ee5837af1ebeb6908ee))
* **taro:** 增加Taro组件,API,Hooks,条件编译相关snippets ([fde62a0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fde62a07a4b5e14fa541265ec2a853e4459c9bcb))
* treeview展示快速创建 ([f7faa23](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f7faa233c138b3d696f909569a268a8ac5a02941))
* **web:** webview面板中类名替换为joycoder ([8480864](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/84808647e2ac017e105123faaf1b7e8a9fec8e9a))


### Bug Fixes

* 本地配置优化 ([2d5073a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/2d5073a8bb901e24f1ba7176ed6c540f451313b1))
* 编辑区隐藏快捷命令和快速创建 ([d4fced0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d4fced01766e7b96e5701663e8b8005c377bba0f))
* 补充上报 ([d54be56](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d54be56aa9d519a8f7e546bd13e4ede6d8f58306))
* 补充数据上报 ([7619e05](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7619e052cf8184053cabd639b76ea709c4ab3255))
* 补充chatgpt问答模式 ([00dbb42](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/00dbb42c5b959686411b63a40125891be10f2ce4))
* 处理本地运行时，报id错误问题。 ([50afeab](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/50afeabf03a30980966d2f7444410be3bacd4b56))
* 代码结构优化 ([01396e3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/01396e3cbbf52d1c9265f423f60d761421d164f9))
* **代码重复检测:** 修复提交ts类型报错 ([5e4d40d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5e4d40d3d462169f693863acc240427acef6c259))
* **代码重复检查:** 修复代码诊断展示数量不准确问题 ([9b17824](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9b17824525ded5a84ee15b8333e67b6a175228af))
* 调整插件包 ([cc52796](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/cc5279663e8e97e65e3535fdb81338bcc7e3c904))
* 更换模版仓库为ssh地址 ([a58247a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a58247a57591429b115857a148866e76e00a9c99))
* 更换配置端文档链接 ([129f922](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/129f922e38e29e5f6602f4b69edac18f5bb7fead))
* 更换logo ([e605a3b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e605a3bb17c16f23ea9773f2007516e47e1b836b))
* 更新成功时增加公告提示&更新失败引导重试 ([5695444](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/56954442b02d726ed4690262b39ad9591cb16466))
* 更新首次安装上报 ([1539f74](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1539f744e71261b822b3ddb49632a735ea0546e8))
* 工作区切换改为远程下发 ([8a0754e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8a0754ec274e16935d2b516750892a2f7a0be386))
* 公共容器增加全局reset.css ([fb5e725](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fb5e725e5bb4d71e94ca3c79a7a96a7cc75d8913))
* 公共容器增加scss配置 ([ca57199](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ca5719902b43e157225281a27a35c744b6e1be68))
* 公共webview新增通过web页面在chrome或vscodez中打开网页能力 ([1298e04](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1298e04172f7b486011f0d45cd42fd14271c57c7))
* 获取项目根路径方法问题修复 ([1c1511a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1c1511a4d6bc294b8887a9957aaf63ff9ad2da25))
* 解决download库导致的打包失败问题 ([c133989](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c1339897c729e581ed4ef50589e4dc4585a551fe))
* 快捷导航改为craft配置 ([30e84ab](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/30e84ab36a124d0c410c4911df30b25cf1a9b293))
* **快速创建:** 读取模板json配置解析问题处理。 ([1291515](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1291515d8c90bae163db872da17efd627497ae5a))
* **快速开始:** 如果远程没有配置name，默认给一个 ([86dac5c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/86dac5c3092a510c0de05c4ad12dec2fbb7236ae))
* **快速开始:** 修复不配置名称报错，兼容templateDirName以/开头的写法 ([348df73](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/348df73231706c5fa5e0579283a8e6c1d6a3053f))
* **快速开始:** 修复未配置formItemConf，不触发兜底bug；兜底逻辑剔除leo类型 ([7698981](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7698981789f4d981c64dcedb216501e1af193d04))
* **快速开始:** 优化快速创建体验:避免重复操作 ([22fcd4d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/22fcd4dae933301614db7adbccf65530a884d377))
* 类型问题修复 ([e812c37](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e812c3729db722c45c966155a8f52fd32a089a33))
* 去掉log以便web页面使用 ([839deb2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/839deb2ee92d245997a743d0876b79035cf01f44))
* 上报信息完善 ([35579a4](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/35579a40dd38537faebe47f5f5f7d12867cb0d62))
* 升级axios,解决header content报错 ([93ad82c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/93ad82c87dc489a39115f4a727e06e47b0eaa7d3))
* 提前插件激活时机 ([cb4b335](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/cb4b3359e3f6d071aed6e966a930d072c947b3d6))
* 完善定制功能开关逻辑 ([3349879](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/334987985d765a322ae75d58ccfe65b475a3b4bd))
* 物料导入模块跳转链接体验优化 ([95e5cbc](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/95e5cbc9db64b894e485103de35663476d7a613b))
* 物料导入时重复安装npm问题修复 ([7558b2c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7558b2cacdedb95fd42115903e66dedb5ba123e1))
* **物料导入:** 首次加载时展示bug ([f6c06d6](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f6c06d668ddb5275de70bf11b7e5be9823d0afde))
* 物料导入修复白屏问题 ([#69](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/69)) ([725f81a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/725f81af45e2224bb4d087a23478294c99af26c6))
* 修复遍历git根目录问题 ([0c9bf94](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0c9bf946204edef44f3baec431b3a33837a199fa))
* 修复打包后引用os路径打包问题 ([3e75796](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3e757968fd0581d3df05a84072b2fe6a5abb9858))
* 修复快捷导航刷新功能 ([91b76aa](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/91b76aa5b8d938e396902c81b4f137b31559b4c8))
* 修复数据上报无效问题 ([2b36bd6](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/2b36bd60d37f242d3d53b75d4fb384293e3629c8))
* 修复填充代码问题 ([f2157b8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f2157b8e27bc41d5da39210f1b76e95e540b97b0))
* 修复无数据是展示异常问题 ([dcd3d80](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/dcd3d80f446f6abb83085662dca9f9f9d936d378))
* 修复物料导入时只能插入第一行代码的问题 ([b24e6f0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b24e6f0448facf02e814d7f56d1b3d2cf5b7e3d5))
* 修复悬浮npm包功能包名识别异常问题 ([1e9b1f0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1e9b1f082d7af9c8fb614423ca8ccda4d930472d))
* 修复自定义编译失败问题 ([6c97e44](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6c97e448310e051ec998db1a7a9463ab18b316e1))
* 修复自动补全标签新增的类名没有定义和悬浮提示问题 ([1d8b3b4](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1d8b3b464f8c0a55ea7a410e37636a9ccc8be660))
* 修复自动更新后需手动重载问题 ([9b5a46d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9b5a46dc967899f96cb88a46e4c05b57846a499d))
* 修复ChatGPT输出换行异常问题 ([51cf0bb](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/51cf0bb110a9010ee88a9213993448e52bd16d07))
* 修复chatgpt无法生成测试用例问题 ([202163a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/202163a3c8783ac72a3db2daa7aa629a36c5c399))
* 修复css中有注释时css definition失效问题 ([7ce1ba9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7ce1ba9b0b39180968b886081c2c413e6c671250))
* 修复hover重复上报问题 ([59dda0f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/59dda0fdc1c05584a9a2f4fe6b4d14028f44b1ce))
* 修复snippets合并时同名key覆盖问题 ([adce42d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/adce42df2a9971696076a4909975e4e60936547c))
* 修复umpkey查询bizid失败问题(更换接口) ([447cb23](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/447cb2340672dd58f21aa9a09b88d48cf1d275d4))
* 修改路径问题。 ([005c6a1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/005c6a1b326f9756c1a10c92d9a9b106f40636cb))
* 优化物料导入编辑框失焦后无法插入代码体验 ([0232885](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0232885cdb8d3a2d2e915b7bd6529c7477de1e19))
* 优化自动更新逻辑 ([489ec7a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/489ec7ae69cf7c3725fde7f2361a4784d286a4ba))
* 优化chatgpt体验:支持操作可配置,优化入口 ([c347e50](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c347e50fbad9abc42100813417552ca7e3f785da))
* 增加私有chatGPTToken配置 ([88e503c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/88e503cd5bbd63620eade0957c27c079639f61cb))
* 增加chatgpt监控&上报 ([763f71e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/763f71e0507da7eaefd486f9e3ff6a8dbc024df4))
* 重复检测支持配置minTokens ([f69197c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f69197c27817f5c2387e090437fe9f0b1404067a))
* 自测体验问题修复 ([ceaff45](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ceaff45e267fb9d2ba4e626f215c783421d938d6))
* **auto import:** 修复输入if+空格后误触发import语句提示问题 ([cae36ef](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/cae36ef0d9310a403dbd45555b32fd45ec896eef))
* **auto import:** 优化import语句补全体验 ([a866bda](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a866bdaf2e94f29cea07ce9bdc5c635d7e602149))
* **chatgpt-web:** 代码类型完善 ([7aaee30](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7aaee308b795e693bd7626a903db77a0ec220c3e))
* **chatgpt:** 避免主按钮被覆盖 ([b4f9811](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b4f9811aea327211ebd26b687503b01d4c65167f))
* **chatgpt:** 补充自定义指令新增跳转 ([a412f8b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a412f8b63c60c07d485a5125a8f8365ebe0328f5))
* **chatgpt:** 初始化对话框时自动回复,节省token ([ff8ca14](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ff8ca140a10d2a0674496fec251448f0cb995b25))
* **chatgpt:** 登录欢迎弹窗展示1.5秒后自动关闭 ([86dabf7](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/86dabf78e61524137787a29449d6c2c25cfaeb93))
* **chatgpt:** 登录态有效期延长至30天 ([9b5017a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9b5017a075a260828ac8ba4f4eb628d08ccc0819))
* **chatgpt:** 调整respMaxTokens入参,尽可能确保完整的输出 ([c8412df](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c8412dfcb1b85f65e693d5fcd69a61570feaeb49))
* **chatgpt:** 对话框样式优化,用更少区域承载更多内容 ([65cba1b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/65cba1bbd141e2c2bc58f7b0ca2eae80a01d80e7))
* **chatgpt:** 聊天窗中直接调用handleAskChatGPT，无需进行code拼接 ([612d185](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/612d185ef639c7170170ca4aff7d56a3f0c2f2d0))
* **chatgpt:** 切换接口逻辑优化 ([02be51d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/02be51d3cda2a68a72a213f6c9fe6236d36b324b))
* **chatgpt:** 收藏问题修复及样式优化 ([10e98a9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/10e98a90d3b55c9b1d5296cf23a5a33db6041fa7))
* **chatgpt:** 替换知识库链接，默认万能模板更新 ([f568086](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f5680865c547df1aed245c5c9d4d6fca171f9dac))
* **chatgpt:** 图标更换，quickpick优化 ([ca76640](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ca766408e214cd03d2f385f5e5ef3c828227266e))
* chatgpt文案优化 ([0081eab](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0081eabd11521ea8c5d659be8c3124cd94647f1b))
* **chatgpt:** 新增工具栏 ([a4b58eb](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a4b58eb0a7bfd211bb758087f526142d9bafdf0d))
* **chatgpt:** 修复不存在profile报错的问题 ([1468d1b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1468d1b126416874fdea11f6951ce66f4e3522e2))
* **chatgpt:** 修复你好时，重试的列表判断错误的问题 ([405765d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/405765d9aa3ba10a84a5bf5117fada614ac4bc4e))
* **chatgpt:** 修复已存在webview时重新打开的体验问题 ([dd49ec2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/dd49ec2da6e4eb3e6a8112f2ecf40d42e047661c))
* **chatgpt:** 修复highlight识别```k出现异常报错白屏的问题 ([0bb0ee5](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0bb0ee542f5c28905576f5b65c23fae773e6496c))
* **chatgpt:** 修复windows下输入换行问题 ([9fd57f2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9fd57f28fbf8e07913571440c3f8c755bb67f192))
* chatgpt样式优化 ([1ecc9ba](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1ecc9baaee336557681fbf6c6b0bbad3a9ea52d3))
* **chatgpt:** 优化代码高亮及输出内容自动滚动体验 ([906a913](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/906a9132a5bfd214a4a518df4d876ae4a5fa3933))
* **chatgpt:** 优化交互体验及相关代码 ([54abcc2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/54abcc2c457ee7c743e63be73224a0b7822b1e1d))
* **chatgpt:** 优化请求超时、频限等容灾体验 ([a8e6b29](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a8e6b29ad1dfce55d26f7928b0418f07b1120cab))
* **chatgpt:** 优化输入框回车逻辑 ([5268036](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5268036ca256d21dc4892035b91dffe76c1b716b))
* **chatgpt:** 优化提示词收藏状态的调用，减少依赖 ([03da801](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/03da801ec9eecf540ad5fa8ed7ce6bb9495203a4))
* **chatgpt:** 优化悬浮按钮样式 ([2e8a522](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/2e8a52220d1cb493989df20cb0ab35dcb42d4b42))
* **chatgpt:** 优化异常情况提示 ([e6bcff2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e6bcff2519e08730296a7233b4b17f759069ffa3))
* **chatgpt:** 优化chatgpt交互区域布局 ([ebf454b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ebf454b047975ac359358cf00ed791f977a8275c))
* **chatgpt:** 优化GPT4用尽时的交互体验 ([eed7f58](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/eed7f588633e11c848d5d2acb335db3a918d6644))
* **chatgpt:** 优化respMaxTokens,缓解上文丢失情况 ([ff9e505](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ff9e50525f97f6e3b0b73ea9e06ddf3c96478207))
* **chatgpt:** 增加chatgpt使用要求 ([fb42eec](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fb42eec512dfed0b56456f878c8514e8d73e9b75))
* **chatgpt:** 支持主题色切换 ([81f7716](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/81f7716b25b6ff6b7092248397228afa6e695dc6))
* chatgpt支持command+回车换行 ([93fd3d1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/93fd3d1bc7c171aa3ef6682f8aaa64e9d7545107))
* **chatgpt:** 主题css文件cdn加载 ([8916841](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/891684174a451676fe03e00dc321e45ddf83e611))
* **chatgpt:** 自定义key的场景，走默认通道去除频限 ([dec7710](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/dec7710166e4f2bfeb9ce2727c64eb9bfc38e577))
* **chatgpt:** cursor编码体验优化&小灯泡增加注释功能 ([061a321](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/061a321e95756612fd0ce71a140bfd72e24df534))
* **chatgpt:** cursor编码支持从小灯泡唤起 ([b77b161](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b77b1619c6d8ea5d24a1cb7f0b07ad8ccd0b911c))
* **cli:** 版本号增加字符串问题修复 ([9025443](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9025443c814cc550ab80451bf0601dd3df6344e2))
* **cli:** 冲突处理 ([defa769](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/defa769624acee5403fcf5e34a59fb16dae8cb57))
* **cli:** 冲突处理 ([8b4e14a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8b4e14a98cf2628df6c16129a8d566fc7de5bb8d))
* **cli:** 冲突处理 ([65d827e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/65d827e4fef30929848c509c6c672fb7386ab16a))
* **cli:** 冲突处理 ([36d5378](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/36d53785c13182c999ef0a528d2c3630aa815b0a))
* **cli:** 调试代码误提交删除 ([ad3b6d3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ad3b6d385ccca329d71495c86b1cb1a00d01b828))
* **cli:** 调整getTargetPath中isTarget优先级提前 ([c1b8923](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c1b8923ce52c0b055388e473844664d81da85744))
* **cli:** 发布失败提示优化 ([c225ddf](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c225ddfbd32781394c1f5cac4a84589991daa227))
* **cli:** 快捷命令行taro编译京喜小程序 ([5645e9b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5645e9b912e15074eafab39ba8435a9e03e94395))
* **cli:** 拼写错误&支持配置timeout等待时间 ([c649a2e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c649a2e60734435e679cf67a898f122ed092fdd1))
* **cli:** 修复pages前面多/导致部分cli工具不识别问题 ([e872fa2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e872fa2afbf3c736db173c0a08cbdb09dbc2b421))
* **codeshow:** 成功登录后继续打卡流程，优化黑色系 ([919f4ec](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/919f4ec7f2023360df98ef0e5476a8a63ee99111))
* **codeshow:** 打卡接口支持远程下发 ([7ccc5c0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7ccc5c0874b6fdbc3c0fb47a1398a925fd5d1968))
* **cursor:** 将用户选择的代码片段约束为整行 ([217cd40](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/217cd40ba949595dfaed84031847dfbc68661ab6))
* **cursor:** 修复编码失焦问题 ([b6fafca](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b6fafcae0cca0239ab083c65144e82d02d3b87af))
* **cursor:** 修复修改代码时缩进不一致问题 ([166b6b8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/166b6b82926c85e75410fd2822535da5e09f0772))
* **cursor:** 修复移除结尾换行时产生的bug ([29f6a91](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/29f6a91d9ee9a802d597277aba4c2dba0b188ea3))
* **cursor:** 修改代码完成后自动打开diff ([41e54f8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/41e54f81d21cfe6d95b1dc81e98a0e6143773ed5))
* **cursor:** 暂时去掉cursor快捷入口功能 ([7bc4f69](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7bc4f69b456d2aebe0715757d189e7ead626f657))
* **doc:** 补充开发贡献文档 ([f075fa5](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f075fa5e38f526be870744c943d84f4c32e80f08))
* **doc:** 补充开发贡献文档 ([1e14d76](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1e14d7619614edae1b8155ac4ec342c4b97f24d7))
* **login:** 解决登录页面偶现503问题 ([5640036](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5640036532abe2b4e52e71b0d99ce4a84d237d32))
* **login:** 优化登录处理，初始化注册router ([26e515d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/26e515db2e12bbe8fcd6711a124a632b81703369))
* **materialimport:** 物料导入体验优化（支持NutUI） ([24e64cc](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/24e64cc398c5fbf11f6b504ac5d6e43d40768123))
* **quick_jump:** 修复文档快速跳转链接被覆盖问题 ([2cf2bc8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/2cf2bc89660bba2fb825f08404e822f4eb5bd94e))
* quick-console体验优化&图片上传接口监控&快速开始样式优化 ([7a283bf](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7a283bf25b76e9638f6937f8477f1e28e7f422f5))
* **quickjump:** 修复umpkey跳kibana问题 ([2b042c9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/2b042c93cd05dbc233a3b915a52e6be9a347db9d))
* **style:** 统一空格缩进 ([9e04caa](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9e04caaa37e9d5f614694bfb196bce8b2b1efe07))
* **update:** 解决windows下自动更新问题,优化自动更新体验 ([0692104](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0692104319b2abd20532161873b051372d424a92))
* web demo改名 ([7c3284d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7c3284dff69ee34b4f5530c1393dabdb9c409a0e))
* windows下自测及问题修复 ([6c8dbbd](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6c8dbbd15f32bc933b51a15fd3f08f87e1ee8d48))

## [2.12.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.11.5...v2.12.0) (2023-09-26)


### Features

* **taro:** 增加Taro组件,API,Hooks,条件编译相关snippets ([fde62a0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fde62a07a4b5e14fa541265ec2a853e4459c9bcb))


### Bug Fixes

* **快速开始:** 优化快速创建体验:避免重复操作 ([22fcd4d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/22fcd4dae933301614db7adbccf65530a884d377))
* **auto import:** 修复输入if+空格后误触发import语句提示问题 ([cae36ef](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/cae36ef0d9310a403dbd45555b32fd45ec896eef))
* **auto import:** 优化import语句补全体验 ([a866bda](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a866bdaf2e94f29cea07ce9bdc5c635d7e602149))
* **login:** 解决登录页面偶现503问题 ([5640036](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5640036532abe2b4e52e71b0d99ce4a84d237d32))

### [2.11.5](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.11.4...v2.11.5) (2023-09-20)


### Bug Fixes

* **chatgpt:** 聊天窗中直接调用handleAskChatGPT，无需进行code拼接 ([612d185](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/612d185ef639c7170170ca4aff7d56a3f0c2f2d0))
* **chatgpt:** 自定义key的场景，走默认通道去除频限 ([dec7710](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/dec7710166e4f2bfeb9ce2727c64eb9bfc38e577))
* **doc:** 补充开发贡献文档 ([f075fa5](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f075fa5e38f526be870744c943d84f4c32e80f08))
* **doc:** 补充开发贡献文档 ([1e14d76](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1e14d7619614edae1b8155ac4ec342c4b97f24d7))

### [2.11.4](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.11.3...v2.11.4) (2023-09-04)


### Bug Fixes

* **quickjump:** 修复umpkey跳kibana问题 ([2b042c9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/2b042c93cd05dbc233a3b915a52e6be9a347db9d))

### [2.11.3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.11.2...v2.11.3) (2023-09-04)


### Bug Fixes

* **cursor:** 将用户选择的代码片段约束为整行 ([217cd40](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/217cd40ba949595dfaed84031847dfbc68661ab6))
* **cursor:** 修复修改代码时缩进不一致问题 ([166b6b8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/166b6b82926c85e75410fd2822535da5e09f0772))
* **cursor:** 修复移除结尾换行时产生的bug ([29f6a91](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/29f6a91d9ee9a802d597277aba4c2dba0b188ea3))
* **cursor:** 修改代码完成后自动打开diff ([41e54f8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/41e54f81d21cfe6d95b1dc81e98a0e6143773ed5))

### [2.11.2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.11.1...v2.11.2) (2023-08-26)


### Bug Fixes

* **chatgpt:** 登录态有效期延长至30天 ([9b5017a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9b5017a075a260828ac8ba4f4eb628d08ccc0819))
* **chatgpt:** 新增工具栏 ([a4b58eb](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a4b58eb0a7bfd211bb758087f526142d9bafdf0d))
* **chatgpt:** 优化悬浮按钮样式 ([2e8a522](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/2e8a52220d1cb493989df20cb0ab35dcb42d4b42))
* **chatgpt:** cursor编码体验优化&小灯泡增加注释功能 ([061a321](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/061a321e95756612fd0ce71a140bfd72e24df534))
* **chatgpt:** cursor编码支持从小灯泡唤起 ([b77b161](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b77b1619c6d8ea5d24a1cb7f0b07ad8ccd0b911c))

### [2.11.1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.11.0...v2.11.1) (2023-08-21)


### Bug Fixes

* **chatgpt:** 修复你好时，重试的列表判断错误的问题 ([405765d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/405765d9aa3ba10a84a5bf5117fada614ac4bc4e))

## [2.11.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.10.1...v2.11.0) (2023-08-21)


### Features

* **chatgpt:** 优化你好的登录体验 ([0357d4c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0357d4c6ec4318f2d146705b9636e50d77c0ce44))
* **chatgpt:** 频限支持GPT4模型 ([7be03be](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7be03bee2c20ce1b9e57e8be95f66e1b081efa3b))


### Bug Fixes

* **chatgpt:** 优化GPT4用尽时的交互体验 ([eed7f58](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/eed7f588633e11c848d5d2acb335db3a918d6644))
* **login:** 优化登录处理，初始化注册router ([26e515d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/26e515db2e12bbe8fcd6711a124a632b81703369))

### [2.10.1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.10.0...v2.10.1) (2023-08-17)


### Bug Fixes

* **chatgpt:** 主题css文件cdn加载 ([8916841](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/891684174a451676fe03e00dc321e45ddf83e611))
* **chatgpt:** 支持主题色切换 ([81f7716](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/81f7716b25b6ff6b7092248397228afa6e695dc6))

## [2.10.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.9.2...v2.10.0) (2023-08-14)


### Features

* **chatgpt:** 支持复制和重试功能 ([d3e03c8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d3e03c8c08b46905443c1ccff06dc088836729c3))


### Bug Fixes

* **chatgpt-web:** 代码类型完善 ([7aaee30](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7aaee308b795e693bd7626a903db77a0ec220c3e))
* **chatgpt:** 优化提示词收藏状态的调用，减少依赖 ([03da801](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/03da801ec9eecf540ad5fa8ed7ce6bb9495203a4))
* **chatgpt:** 收藏问题修复及样式优化 ([10e98a9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/10e98a90d3b55c9b1d5296cf23a5a33db6041fa7))

### [2.9.2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.9.1...v2.9.2) (2023-08-03)


### Bug Fixes

* 物料导入修复白屏问题 ([#69](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/69)) ([725f81a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/725f81af45e2224bb4d087a23478294c99af26c6))

### [2.9.1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.9.0...v2.9.1) (2023-07-31)


### Bug Fixes

* **chatgpt:** 修复不存在profile报错的问题 ([1468d1b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1468d1b126416874fdea11f6951ce66f4e3522e2))

## [2.9.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.8.1...v2.9.0) (2023-07-31)


### Features

* **chatgpt:** 支持提示词收藏&快速检索历史提示词 ([9a054e7](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9a054e781af8bf29c734a4f49509cc375ac745cb))
* **chatgpt:** 新增个性化指令特性，配置项UI交互优化 ([edac604](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/edac604754105a364b98a15d4711d8baa7e07e72))
* **物料导入:** 支持配置二级Tab及版本选择 ([8d21838](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8d21838e4bdac83bd50a2a1d47159ca9453ec437))


### Bug Fixes

* **chatgpt:** 补充自定义指令新增跳转 ([a412f8b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a412f8b63c60c07d485a5125a8f8365ebe0328f5))
* **chatgpt:** 避免主按钮被覆盖 ([b4f9811](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b4f9811aea327211ebd26b687503b01d4c65167f))
* **物料导入:** 首次加载时展示bug ([f6c06d6](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f6c06d668ddb5275de70bf11b7e5be9823d0afde))

### [2.8.1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.8.0...v2.8.1) (2023-07-19)


### Bug Fixes

* **chatgpt:** 替换知识库链接，默认万能模板更新 ([f568086](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f5680865c547df1aed245c5c9d4d6fca171f9dac))

## [2.8.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.7.0...v2.8.0) (2023-07-14)


### Features

* **chatgpt:** 支持调用集团ChatGPT网关 ([b6f4ed3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b6f4ed313cb2fe7b3a253c21a2f64fa0485795ad))
* **chatgpt:** 支持insiders版本vscode登录 ([857156a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/857156a34af1a2ab8dc95e5c12c9d76f382053eb))


### Bug Fixes

* 修复ChatGPT输出换行异常问题 ([51cf0bb](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/51cf0bb110a9010ee88a9213993448e52bd16d07))
* 修复snippets合并时同名key覆盖问题 ([adce42d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/adce42df2a9971696076a4909975e4e60936547c))
* **chatgpt:** 登录欢迎弹窗展示1.5秒后自动关闭 ([86dabf7](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/86dabf78e61524137787a29449d6c2c25cfaeb93))

## [2.7.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.6.0...v2.7.0) (2023-07-07)


### Features

* **快速开始:** 支持Taro项目/页面/页面组件 ([0651860](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0651860268eefbbbd8952b0bce6511b436e64803))
* **chatgpt:** 通过你好判断登录态和GPT服务可用性 ([c37a49b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c37a49bb60cbff5f37c5017c133f87d564ed5943))
* **chatgpt:** 支持私域向量知识库 ([acdbe7e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/acdbe7e66972796a1e6b3466f9f1bb3f825b101b))


### Bug Fixes

* **chatgpt:** 修复已存在webview时重新打开的体验问题 ([dd49ec2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/dd49ec2da6e4eb3e6a8112f2ecf40d42e047661c))

## [2.6.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.5.3...v2.6.0) (2023-06-02)


### Features

* **chatgpt:** 支持用户选择不同模型 ([c108417](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c108417230f5d71224b8a273a7336ca69b43a1eb))


### Bug Fixes

* **chatgpt:** 图标更换，quickpick优化 ([ca76640](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ca766408e214cd03d2f385f5e5ef3c828227266e))
* **chatgpt:** 优化respMaxTokens,缓解上文丢失情况 ([ff9e505](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ff9e50525f97f6e3b0b73ea9e06ddf3c96478207))

### [2.5.3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.5.2...v2.5.3) (2023-06-02)


### Bug Fixes

* **快速开始:** 修复未配置formItemConf，不触发兜底bug；兜底逻辑剔除leo类型 ([7698981](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7698981789f4d981c64dcedb216501e1af193d04))

### [2.5.2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.5.1...v2.5.2) (2023-05-26)


### Bug Fixes

* **chatgpt:** 修复highlight识别```k出现异常报错白屏的问题 ([0bb0ee5](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0bb0ee542f5c28905576f5b65c23fae773e6496c))

### [2.5.1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.5.0...v2.5.1) (2023-05-26)


### Bug Fixes

* **快速开始:** 如果远程没有配置name，默认给一个 ([86dac5c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/86dac5c3092a510c0de05c4ad12dec2fbb7236ae))
* **快速开始:** 修复不配置名称报错，兼容templateDirName以/开头的写法 ([348df73](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/348df73231706c5fa5e0579283a8e6c1d6a3053f))
* **chatgpt:** 优化异常情况提示 ([e6bcff2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e6bcff2519e08730296a7233b4b17f759069ffa3))

## [2.5.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.4.3...v2.5.0) (2023-05-09)


### Features

* **cli&quickstart:** 适配Leo：支持Leo快捷命令行和常用Leo模板快速创建 ([#34](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/34)) ([23a5078](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/23a50787ea63322d9ff019e82cc6f737f76b88e1))


### Bug Fixes

* **chatgpt:** 调整respMaxTokens入参,尽可能确保完整的输出 ([c8412df](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c8412dfcb1b85f65e693d5fcd69a61570feaeb49))

### [2.4.3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.4.2...v2.4.3) (2023-04-28)


### Bug Fixes

* **chatgpt:** 初始化对话框时自动回复,节省token ([ff8ca14](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ff8ca140a10d2a0674496fec251448f0cb995b25))
* **chatgpt:** 增加chatgpt使用要求 ([fb42eec](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fb42eec512dfed0b56456f878c8514e8d73e9b75))
* **update:** 解决windows下自动更新问题,优化自动更新体验 ([0692104](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0692104319b2abd20532161873b051372d424a92))

### [2.4.2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.4.1...v2.4.2) (2023-04-26)


### Bug Fixes

* **cursor:** 暂时去掉cursor快捷入口功能 ([7bc4f69](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7bc4f69b456d2aebe0715757d189e7ead626f657))

### [2.4.1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.4.0...v2.4.1) (2023-04-26)


### Bug Fixes

* **cursor:** 修复编码失焦问题 ([b6fafca](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b6fafcae0cca0239ab083c65144e82d02d3b87af))

## [2.4.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.3.0...v2.4.0) (2023-04-26)


### Features

* 支持cursor类似的交互能力 ([ca6335c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ca6335c9006801d65f9185a2d0a42b9616f95d0d))
* **chatgpt:** chatgpt服务切换为java版本 ([10f541f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/10f541f11acb7da6f442d52662a810f909c9c492))


### Bug Fixes

* **chatgpt:** 切换接口逻辑优化 ([02be51d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/02be51d3cda2a68a72a213f6c9fe6236d36b324b))
* **chatgpt:** 修复windows下输入换行问题 ([9fd57f2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9fd57f28fbf8e07913571440c3f8c755bb67f192))
* **chatgpt:** 优化交互体验及相关代码 ([54abcc2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/54abcc2c457ee7c743e63be73224a0b7822b1e1d))
* **materialimport:** 物料导入体验优化（支持NutUI） ([24e64cc](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/24e64cc398c5fbf11f6b504ac5d6e43d40768123))

## [2.3.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.2.0...v2.3.0) (2023-04-19)


### Features

* 增加chatgpt快捷入口 ([a7aed03](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a7aed03fc46e445cc28d7f27ec207e7d1de6a455))
* 支持ERP登录态打通 ([01868a0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/01868a0a30bcb16df04f05c138df450071a908ec))
* chatgpt代码块增加插入和复制操作 ([3599ddd](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3599ddd9667117c49f17ad9e13356c8379757b12))


### Bug Fixes

* **chatgpt:** 对话框样式优化,用更少区域承载更多内容 ([65cba1b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/65cba1bbd141e2c2bc58f7b0ca2eae80a01d80e7))
* **chatgpt:** 优化代码高亮及输出内容自动滚动体验 ([906a913](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/906a9132a5bfd214a4a518df4d876ae4a5fa3933))
* **chatgpt:** 优化请求超时、频限等容灾体验 ([a8e6b29](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a8e6b29ad1dfce55d26f7928b0418f07b1120cab))
* **chatgpt:** 优化输入框回车逻辑 ([5268036](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5268036ca256d21dc4892035b91dffe76c1b716b))
* **chatgpt:** 优化chatgpt交互区域布局 ([ebf454b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ebf454b047975ac359358cf00ed791f977a8275c))

## [2.2.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.1.2...v2.2.0) (2023-03-20)


### Features

* 新增无用export代码扫描和无用文件扫描功能 ([4beae97](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4beae9701990635d971de61533f5bef118b04845))
* 新增HTML转CSS/SCSS/LESS功能 ([a7a6bf2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a7a6bf22d08f2f2a3c329238a1f03eb487715785))
* 支持插件市场一键安装 ([67ba988](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/67ba9880d448d448af0e09b753ada4828ad0bf00))
* chatgpt增加提前停止功能，支持配置私有key ([e5d769f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e5d769f8841ac50bdc57202d7cfada9be126a61e))


### Bug Fixes

* chatgpt文案优化 ([0081eab](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0081eabd11521ea8c5d659be8c3124cd94647f1b))

### [2.1.2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.1.1...v2.1.2) (2023-03-06)


### Bug Fixes

* 修复物料导入时只能插入第一行代码的问题 ([b24e6f0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b24e6f0448facf02e814d7f56d1b3d2cf5b7e3d5))
* 优化自动更新逻辑 ([489ec7a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/489ec7ae69cf7c3725fde7f2361a4784d286a4ba))
* 优化chatgpt体验:支持操作可配置,优化入口 ([c347e50](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c347e50fbad9abc42100813417552ca7e3f785da))

### [2.1.1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.1.0...v2.1.1) (2023-02-08)


### Bug Fixes

* 修复自动更新后需手动重载问题 ([9b5a46d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9b5a46dc967899f96cb88a46e4c05b57846a499d))
* 修复chatgpt无法生成测试用例问题 ([202163a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/202163a3c8783ac72a3db2daa7aa629a36c5c399))

## [2.1.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v2.0.0...v2.1.0) (2023-02-07)


### Features

* **chatgpt:** 切换到远程代理服务 ([b39aec6](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b39aec60eea4f25cfccc118636114d990a87a1be))

## [2.0.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.4.0...v2.0.0) (2022-12-12)


### Features

*  优化远程命令行解析方式，调整顺序 ([a27de21](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a27de21f5d81d5149362b9bd6746c5c420b227bf))
*  增加chagpt ([e9dfaa2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e9dfaa2c184d1acde8c9442cc5db546baaf8feb1))
*  chatgpt异常交互提醒，去除日志提升性能 ([c815a0c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c815a0c31d89706369bc007d21888f6e28eea2f0))
* 插件配置数据切换至水滴数据源 ([8cbe2ed](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8cbe2ed418377edaab7e30d4436c4d8822483ec2))
* 恢复插件包功能,只附带安装轻量且较通用的插件 ([5c97b38](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5c97b38db914c7e2cf65b7c0f9a41119bea36304))
* 快速开始支持第三方仓库及部分代码重构 ([f15e0d0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f15e0d09b6fc6b7e11b8ddc9c9140a3a0812e373))
* 命令行工厂重构，支持远程配置化 ([ca18ae0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ca18ae03217389b357eca92ad51abe6fa7f36a85))
* 命令行执行拆分本地&远程，修复beta发布问题 ([fac16fc](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fac16fc25e214fca6661c9c3d9f4212e1a9d0686))
* 搜索增加快捷键 ([11ab12e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/11ab12ed849fe5a16d7bf7a175abd6c983d90fb5))
* 新增快速切换工作区&主接口可用率上报等 ([0232d41](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0232d41e0f787c466317f150268b1bf854ca89dd))
* 新增快速添加console代码功能 ([b090feb](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b090feb27c4d1b4a61a833d011fb38e26c5d978d))
* 支持远程配置来开关非通用性的功能 ([833249a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/833249a237f495afc402814f236f5528f763fa5f))


### Bug Fixes

* 编辑区隐藏快捷命令和快速创建 ([d4fced0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d4fced01766e7b96e5701663e8b8005c377bba0f))
* 补充chatgpt问答模式 ([00dbb42](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/00dbb42c5b959686411b63a40125891be10f2ce4))
* 更换模版仓库为ssh地址 ([a58247a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a58247a57591429b115857a148866e76e00a9c99))
* 更换配置端文档链接 ([129f922](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/129f922e38e29e5f6602f4b69edac18f5bb7fead))
* 更新成功时增加公告提示&更新失败引导重试 ([5695444](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/56954442b02d726ed4690262b39ad9591cb16466))
* 更新首次安装上报 ([1539f74](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1539f744e71261b822b3ddb49632a735ea0546e8))
* 工作区切换改为远程下发 ([8a0754e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8a0754ec274e16935d2b516750892a2f7a0be386))
* 类型问题修复 ([e812c37](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e812c3729db722c45c966155a8f52fd32a089a33))
* 上报信息完善 ([35579a4](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/35579a40dd38537faebe47f5f5f7d12867cb0d62))
* 升级axios,解决header content报错 ([93ad82c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/93ad82c87dc489a39115f4a727e06e47b0eaa7d3))
* 提前插件激活时机 ([cb4b335](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/cb4b3359e3f6d071aed6e966a930d072c947b3d6))
* 完善定制功能开关逻辑 ([3349879](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/334987985d765a322ae75d58ccfe65b475a3b4bd))
* 修复快捷导航刷新功能 ([91b76aa](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/91b76aa5b8d938e396902c81b4f137b31559b4c8))
* 修复无数据是展示异常问题 ([dcd3d80](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/dcd3d80f446f6abb83085662dca9f9f9d936d378))
* 修复悬浮npm包功能包名识别异常问题 ([1e9b1f0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1e9b1f082d7af9c8fb614423ca8ccda4d930472d))
* 增加私有chatGPTToken配置 ([88e503c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/88e503cd5bbd63620eade0957c27c079639f61cb))
* 增加chatgpt监控&上报 ([763f71e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/763f71e0507da7eaefd486f9e3ff6a8dbc024df4))
* 自测体验问题修复 ([ceaff45](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ceaff45e267fb9d2ba4e626f215c783421d938d6))
* chatgpt样式优化 ([1ecc9ba](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1ecc9baaee336557681fbf6c6b0bbad3a9ea52d3))
* chatgpt支持command+回车换行 ([93fd3d1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/93fd3d1bc7c171aa3ef6682f8aaa64e9d7545107))
* quick-console体验优化&图片上传接口监控&快速开始样式优化 ([7a283bf](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7a283bf25b76e9638f6937f8477f1e28e7f422f5))
* windows下自测及问题修复 ([6c8dbbd](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6c8dbbd15f32bc933b51a15fd3f08f87e1ee8d48))

## [1.4.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.3.1...v1.4.0) (2022-11-03)


### Features

* 支持插件自动安装更新 ([f176bf5](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f176bf50acd1bec36149ee6e58b327a920743cc1))


### Bug Fixes

* 本地配置优化 ([2d5073a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/2d5073a8bb901e24f1ba7176ed6c540f451313b1))
* 代码结构优化 ([01396e3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/01396e3cbbf52d1c9265f423f60d761421d164f9))
* 解决download库导致的打包失败问题 ([c133989](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c1339897c729e581ed4ef50589e4dc4585a551fe))

### [1.3.1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.3.0...v1.3.1) (2022-10-12)


### Bug Fixes

* 修复自定义编译失败问题 ([6c97e44](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6c97e448310e051ec998db1a7a9463ab18b316e1))

## [1.3.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.2.0...v1.3.0) (2022-09-28)


### Features

* 增加git发布管理后台功能 ([4139cc1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4139cc180d117a63e8bac6f46293e047196871c6))
* css中插入图片时增加宽高信息 ([acda4cf](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/acda4cf4045cefcdd2d046dcab62c4a0fcbdcb9d))


### Bug Fixes

* 修复遍历git根目录问题 ([0c9bf94](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0c9bf946204edef44f3baec431b3a33837a199fa))

## [1.2.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.1.1...v1.2.0) (2022-08-25)


### Features

* **cli:** 代码优化，命令长度为0 的异常流判断统一处理 ([3b3fb69](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3b3fb6954cec7313d75f35fd53f5ffee7b8c4dfb))
* **cli:** 增加不限制目录的legos命令 ([133012e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/133012e28a3aabdcbb52442b373a2a144f74a2d3))


### Bug Fixes

* **cli:** 调试代码误提交删除 ([ad3b6d3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ad3b6d385ccca329d71495c86b1cb1a00d01b828))

### [1.1.1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.1.0...v1.1.1) (2022-06-24)


### Bug Fixes

* 修复umpkey查询bizid失败问题(更换接口) ([447cb23](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/447cb2340672dd58f21aa9a09b88d48cf1d275d4))

## [1.1.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v1.0.0...v1.1.0) (2022-06-18)


### Features

* **插件包:** 下线插件包特性,只在使用文档推荐,不做强制安装 ([acaa587](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/acaa5873ee9cc0f081a0cfd1ceef9b674a76a21a))
* **cli:** 编译taro京喜小程序，判断wxapp是否有编译产物来调整timeout ([5e21ac9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5e21ac9c14d56bbcfff862875d2980de0af7caf1))
* **cli:** 发布beta包时远程版本选择beta和latest中的较大值 ([78c3ad9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/78c3ad9725f77bdd59f0d43f053f5e7b3861e222))
* **cli:** 根目录提示文案优化 ([52e6c36](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/52e6c365eef096fe652039acf2d29ec47ebd8f74))
* **cli:** 更新beta的基线改为npm verisons中最后1个 ([13ce290](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/13ce29034a2c198e4094f5aef924a007d8d7b300))
* **cli:** 提示文案调整 ([fc36aa1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fc36aa179086f360aaf89d210386812ab919e501))
* **cli:** 支持任意仓库发布beta版npm包 ([05b7b07](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/05b7b074f4733e53225776cea56c02fbc175d035))


### Bug Fixes

* **cli:** 版本号增加字符串问题修复 ([9025443](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9025443c814cc550ab80451bf0601dd3df6344e2))
* **cli:** 调整getTargetPath中isTarget优先级提前 ([c1b8923](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c1b8923ce52c0b055388e473844664d81da85744))
* **cli:** 发布失败提示优化 ([c225ddf](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c225ddfbd32781394c1f5cac4a84589991daa227))
* **cli:** 快捷命令行taro编译京喜小程序 ([5645e9b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5645e9b912e15074eafab39ba8435a9e03e94395))
* **cli:** 拼写错误&支持配置timeout等待时间 ([c649a2e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c649a2e60734435e679cf67a898f122ed092fdd1))
* **quick_jump:** 修复文档快速跳转链接被覆盖问题 ([2cf2bc8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/2cf2bc89660bba2fb825f08404e822f4eb5bd94e))

## [1.0.0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v0.0.3...v1.0.0) (2022-06-13)


### Features

* **代码重复检查:** 查重参数调整 ([5b98ada](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5b98ada06e84312431a0d6812bec683aa6c613df))
* **代码重复检查:** 代码路径调整 ([66cc5b2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/66cc5b2bcf27e329c6d5bcc7833608c26c70ebae))
* **代码重复检查:** 提示文案修改为中文 ([7842c25](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7842c25b03772f11bddc48caed4c52a9e59b3449))
* 代码重复检查支持选中多文件 ([e7b20e5](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e7b20e51f284e6a38698caa2023cd14d58dd3457))
* 调整打开ViewLoader里的viewColumn配置判断逻辑。 ([b2ce21a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b2ce21a137c5776ec76b4370d7b3a1dd78bbda4d))
* 更新自定义编译 ([8f36412](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8f364125ddfe670dd83423aca52665338d8d3f9e))
* 工作目录支持相对、绝对、项目根目录 ([761a942](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/761a94219d83e1af27c6941d767ac278aade489f))
* **快速创建:** 处理写入路径时减少变动。 ([620b1f8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/620b1f8c85e17b7565d64eae47575fe72a9497b7))
* **快速创建:** 创建时替换文本。 ([29ff828](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/29ff82849e4d012224df9e7fd5d9d0674b2c28ba))
* **快速创建:** 埋点。 ([6ef8976](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6ef8976787348e16f1244209a5007960f19318d1))
* **快速创建:** 模板列表样式调整。 ([4f54e3f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4f54e3fbb4fd4c4fb130abb47d51fed7c8957246))
* **快速创建:** 新增页面增加路径写入。 ([3fa36f1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3fa36f15132f4da7ba1e6a70274d1acf42e0ad90))
* 统一message、清理多余console。 ([f119cc8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f119cc856275d11929b0c4426fa1c931ac7eaea5))
* 新增代码重复检查能力 ([e7c4a02](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e7c4a021cfb3e601787217cdb02e340a847dd105))
* 新增快速创建插件。 ([fbb8865](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fbb886565b7de50dd9474a208ad7ba48179dd9e6))
* 新增推荐插件 ([7572b11](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7572b11c4f5b9a3ebf3cf0a81468ff4b725f7408))
* 新增悬浮ip时直达webadmin能力 ([cbae565](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/cbae565372f34eb53a11b35db1f6d5e75ec6e60e))
* 新增右击页面目录查看该页面badjs功能 ([7c0ed78](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7c0ed789c141ca99739900f9f372c05e40a4ed7e))
* 新增自定义配置命令 ([fdaf654](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fdaf654d6bc85018b222936ff9ebda32c83b0f4b))
* 新增git辅助功能 ([74b9c8d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/74b9c8d9f0d01013aa272f457622a3feff5d4603))
* 自定义命令编译支持多选 ([d9844e8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d9844e800c729cb8d1e2aeff9dddce0777489a44))
* **cli:** 递归往父级查找目标文件的逻辑收敛 ([512f010](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/512f0109a30ba0d9614a884ce7403923ab3d2e00))
* **cli:** 关闭命令行后commandBar隐藏 ([4b2e859](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4b2e85958dd181751a0a64496acaab67f011a623))
* **cli:** 使用shared/logger标准化展示消息 ([5d12b65](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5d12b653682743af2e8190b725b083f3526ac872))
* **cli:** 文件窗口内右键也展示快捷命令行 ([10d880d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/10d880d02009f7f5f2f26e477555123854822a3b))
* **cli:** 增加关闭所有命令行 ([4e19318](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/4e193189c2963f15a527e53587c9bf3258ec53cb))
* **cli:** 增加使用次数上报 ([dd158f7](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/dd158f78c6e2c080438f32b1c1f488a478672cca))
* **cli:** 支持jxapp单页发布&基础库发beta版本 ([3f4039f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3f4039fd6a3892bca6e74453ba46c4997cf226f7))
* **cli:** 自定义配置命令行统一执行器 ([f3450eb](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f3450ebb22b5a448a7c135aaef5fef9407921f7a))
* **cli:** jxapp项目H5单页开发能力 ([6494b81](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6494b81521b494dffcd6eec9b71e3bb182a54cf4))
* treeview展示快速创建 ([f7faa23](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f7faa233c138b3d696f909569a268a8ac5a02941))


### Bug Fixes

* 补充上报 ([d54be56](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d54be56aa9d519a8f7e546bd13e4ede6d8f58306))
* 处理本地运行时，报id错误问题。 ([50afeab](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/50afeabf03a30980966d2f7444410be3bacd4b56))
* **代码重复检测:** 修复提交ts类型报错 ([5e4d40d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/5e4d40d3d462169f693863acc240427acef6c259))
* **代码重复检查:** 修复代码诊断展示数量不准确问题 ([9b17824](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9b17824525ded5a84ee15b8333e67b6a175228af))
* 调整插件包 ([cc52796](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/cc5279663e8e97e65e3535fdb81338bcc7e3c904))
* 获取项目根路径方法问题修复 ([1c1511a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1c1511a4d6bc294b8887a9957aaf63ff9ad2da25))
* **快速创建:** 读取模板json配置解析问题处理。 ([1291515](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1291515d8c90bae163db872da17efd627497ae5a))
* 修复数据上报无效问题 ([2b36bd6](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/2b36bd60d37f242d3d53b75d4fb384293e3629c8))
* 修复自动补全标签新增的类名没有定义和悬浮提示问题 ([1d8b3b4](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1d8b3b464f8c0a55ea7a410e37636a9ccc8be660))
* 修复hover重复上报问题 ([59dda0f](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/59dda0fdc1c05584a9a2f4fe6b4d14028f44b1ce))
* 修改路径问题。 ([005c6a1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/005c6a1b326f9756c1a10c92d9a9b106f40636cb))
* 重复检测支持配置minTokens ([f69197c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f69197c27817f5c2387e090437fe9f0b1404067a))
* **cli:** 冲突处理 ([defa769](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/defa769624acee5403fcf5e34a59fb16dae8cb57))
* **cli:** 冲突处理 ([8b4e14a](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8b4e14a98cf2628df6c16129a8d566fc7de5bb8d))
* **cli:** 冲突处理 ([65d827e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/65d827e4fef30929848c509c6c672fb7386ab16a))
* **cli:** 冲突处理 ([36d5378](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/36d53785c13182c999ef0a528d2c3630aa815b0a))
* **cli:** 修复pages前面多/导致部分cli工具不识别问题 ([e872fa2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e872fa2afbf3c736db173c0a08cbdb09dbc2b421))
* **style:** 统一空格缩进 ([9e04caa](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9e04caaa37e9d5f614694bfb196bce8b2b1efe07))

### [0.0.3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v0.0.2...v0.0.3) (2022-05-23)


### Features

* 树视图增加问题反馈等入口 ([8904efc](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/8904efca3b329f58753f938b4c45b3d463ea9fcf))
* 物料导入体验优化 ([3a8d8e4](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3a8d8e4235347012fb85ce75935d8d1128ea49ce))
* 新增快速搜索(组件文档、接口查询、kibana 查询) ([#1](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/1)) ([a2aaa85](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a2aaa85ded827f0636b6ba68461b5b842711723c))
* 新增物料导入能力 ([ce11403](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ce1140381f78aebf00591521dff0343696edbc4f))
* 优化标签自动补全,并在补全时自动添加css代码 ([c1c26c0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c1c26c0fbde2fc202b3f85f34952d7ae54c611dd))
* 增加标签补全为cssmodule能力 ([e7cdcb3](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e7cdcb33f940a6c84215452a9fccec30663a30e1))
* 增加插件包能力,安装JoyCoder时自动安装其他推荐插件 ([c934456](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c93445646c1ef86284c4136eb1824ef3588915b7))
* 增加公共跳转方法 ([30cc0d0](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/30cc0d08c139a0ff2f68aea84d66a1d91c96ceea))
* 增加公共webview ([f6ebab9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f6ebab908df992dd7cf1a3c6e5c4af04a78f2ad6))
* 增加快捷操作树视图 ([2041281](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/20412813ae8a26ab8bdb18ee89f5e11a385e7b72))
* 增加快捷导航树视图 ([68e23cc](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/68e23cc25a8c66c73accf43d236709bc2fa2bcaf))
* 增加数据上报 ([6372c11](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/6372c11faa6b90da7d02c94ebd8dea7084e058cb))
* 增加远程配置获取及版本更新提示能力 ([9a2975e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/9a2975ed6295e528269f17ea4751ff7fac11bcb2))
* 增加css 定义功能(css预览及跳转定位) ([24e430b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/24e430b3d745c5d55fe868e69c7d2871e43a9389))
* 增加css转为cssmodule能力 ([d8bcf44](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/d8bcf4409411ca0332c69f120111d325666f2681))
* 增加import代码补全 ([b7f7315](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/b7f7315d9159012824565f946856ed2233713026))
* 增加snippet代码片段能力 ([3c22582](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3c22582292673acb35e979a9e15dc14d09a5b87c))
* snippet支持远程及本地配置 ([32f07ae](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/32f07aee2333c679d49c0ee5837af1ebeb6908ee))


### Bug Fixes

* 补充数据上报 ([7619e05](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7619e052cf8184053cabd639b76ea709c4ab3255))
* 更换logo ([e605a3b](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/e605a3bb17c16f23ea9773f2007516e47e1b836b))
* 公共容器增加全局reset.css ([fb5e725](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fb5e725e5bb4d71e94ca3c79a7a96a7cc75d8913))
* 公共容器增加scss配置 ([ca57199](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/ca5719902b43e157225281a27a35c744b6e1be68))
* 公共webview新增通过web页面在chrome或vscodez中打开网页能力 ([1298e04](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/1298e04172f7b486011f0d45cd42fd14271c57c7))
* 快捷导航改为craft配置 ([30e84ab](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/30e84ab36a124d0c410c4911df30b25cf1a9b293))
* 去掉log以便web页面使用 ([839deb2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/839deb2ee92d245997a743d0876b79035cf01f44))
* 物料导入模块跳转链接体验优化 ([95e5cbc](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/95e5cbc9db64b894e485103de35663476d7a613b))
* 物料导入时重复安装npm问题修复 ([7558b2c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7558b2cacdedb95fd42115903e66dedb5ba123e1))
* 修复填充代码问题 ([f2157b8](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/f2157b8e27bc41d5da39210f1b76e95e540b97b0))
* 修复css中有注释时css definition失效问题 ([7ce1ba9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7ce1ba9b0b39180968b886081c2c413e6c671250))
* 优化物料导入编辑框失焦后无法插入代码体验 ([0232885](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0232885cdb8d3a2d2e915b7bd6529c7477de1e19))
* web demo改名 ([7c3284d](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7c3284dff69ee34b4f5530c1393dabdb9c409a0e))

### [0.0.2](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/compare/v0.0.1...v0.0.2) (2022-04-08)


### Features

* 条件编译自动编码初步 ([0a97926](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/0a979264d2c6939c5c351e1d4cde70ece9e815d0))
* 新增上传剪切板中截图特性 ([fa3dd8e](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/fa3dd8e10af8db294a2e91baaf8bad9541e1c8ad))
* 新增JoyCoder日志输出渠道 ([846a5d7](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/846a5d7e0c88d0d3db44dcbe8c375e4e00939abd))
* 增加悬浮提示能力 ([28e89f9](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/28e89f9db11775daebff6e13efb9b42d35d56a95))


### Bug Fixes

* 修复打包后引用os路径打包问题 ([3e75796](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/3e757968fd0581d3df05a84072b2fe6a5abb9858))

### 0.0.1 (2022-01-27)


### Features

* 使用vscode插件脚手架生成插件代码 ([a970c68](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/a970c68ff22d65c1e1e624ea7f373e07a0cebd2c))
* 增加侧边栏入口 ([c14e95c](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/c14e95c98e220d113efeae023b7a1a9a8be125d0))
* 增加图片上传功能 ([7702dea](https://coding.jd.com/JoyCoder/JoyCoder-VSCode/commit/7702deae4f4255653d97cba8fb6e45b77d529c4c))
