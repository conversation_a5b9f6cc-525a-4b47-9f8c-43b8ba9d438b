// Place your settings in this file to overwrite default and user settings.
{
  "files.exclude": {
      "out": false, // set this to true to hide the "out" folder with the compiled JS files
      "dist": false // set this to true to hide the "dist" folder with the compiled JS files
  },
  "search.exclude": {
      "out": true, // set this to false to include "out" folder in search results
      "dist": true // set this to false to include "dist" folder in search results
  },
  // Turn off tsc task auto detection since we have the necessary tasks as npm scripts
  "typescript.tsc.autoDetect": "off",
  "[html]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode",
  },
  "[javascript]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode",
  },
  "git.ignoreLimitWarning": true,
  "i18n-ally.localesPaths": [
    "packages/web/src/messages"
  ],
  "JoyCode.switch.AgentView": false,
  "JoyCoder.switch.AgentView": true,
}
